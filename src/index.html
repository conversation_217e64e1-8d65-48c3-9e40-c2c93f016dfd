<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />

  <title>Safe Work Permit</title>

  <base href="./" />
  <meta name="viewport"
    content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="format-detection" content="telephone=no" />
  <meta name="msapplication-tap-highlight" content="no" />
  <!-- FontAwesome Free CDN - temporary solution -->
 <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

  <link rel="icon" type="image/x-icon" href="./assets/icon/favicon.ico">
  <script src="cordova.js"></script>
  
  <!-- add to homescreen for ios -->
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />

  <!-- Unvired Kernel js-->
  <script src="assets/js/sql.js"></script>
  <script src="assets/js/unvired-db-worker.js"></script>


  <script src='assets/js/formio.full.min.js'></script>
  
  <link href="https://fonts.googleapis.com/css?family=Titillium+Web" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

  <!-- Odometer for animated counters -->
  <script src="assets/js/odometer.min.js"></script>
  <link rel="stylesheet" href="assets/css/odometer-theme-minimal.css">

  <!-- Google Maps API with drawing and geometry libraries -->
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBeUrdEDgChiCqzuJ55N67_C_P0e01C5H0&libraries=drawing,geometry"></script>

  <!-- FormIO and related scripts for form rendering -->
  <script src="assets/formio.full.min.js"></script>


</head>

<body class="ig-typography ig-scrollbar">
  <app-root></app-root>
</body>

</html>
