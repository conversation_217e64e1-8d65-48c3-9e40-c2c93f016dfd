<ion-header>
  <ion-toolbar mode="ios" color="light">
    <ion-title color="dark" class="modal-title">Assign User For Review</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" color="medium" (click)="cancel()">
        <ion-icon name="close" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<div class="modal-container">
  <!-- Fixed Header Section -->
  <div class="fixed-header">
    <!-- Custom Segment Tabs -->
    <div class="segment-container">
      <div class="segment-tabs">
        <div class="segment-tab"
             [class.active]="segmentValue === 'Internal'"
             (click)="segmentValue = 'Internal'">
          <span>Internal</span>
        </div>
        <div class="segment-tab"
             [class.active]="segmentValue === 'External'"
             (click)="segmentValue = 'External'"
             *ngIf="isExternal">
          <span>External</span>
        </div>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <ion-searchbar
        [(ngModel)]="searchText"
        (ionInput)="filterUsers()"
        placeholder="Search users..."
        show-clear-button="focus"
        class="custom-searchbar">
      </ion-searchbar>
    </div>
  </div>

  <!-- Scrollable Content Section -->
  <div class="scrollable-content">
    <div class="user-list-container">
      <div [ngSwitch]="segmentValue">
        <div *ngSwitchCase="'Internal'">
          <div class="user-item"
               *ngFor="let user of filteredInternalUsers"
               [class.selected]="selectedUserId === user.USER_ID"
               (click)="selectUser(user)">
            <div class="user-avatar">
              <ion-icon name="person" color="primary"></ion-icon>
            </div>
            <div class="user-info">
              <span class="user-name">{{user.FIRST_NAME}} {{user.LAST_NAME}}</span>
            </div>
          </div>
        </div>

        <div *ngSwitchCase="'External'">
          <div class="user-item"
               *ngFor="let user of filteredExternalUsers"
               [class.selected]="selectedUserId === user.USER_ID"
               (click)="selectUser(user)">
            <div class="user-avatar">
              <ion-icon name="person" color="primary"></ion-icon>
            </div>
            <div class="user-info">
              <span class="user-name">{{user.FIRST_NAME}} {{user.LAST_NAME}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>`
  </div>

  <!-- Fixed Footer Section -->
  <div class="fixed-footer">
    <div class="action-buttons">
      <ion-button fill="clear" color="medium" (click)="cancel()">
        Cancel
      </ion-button>
      <ion-button
        fill="solid"
        color="primary"
        [disabled]="!selectedUserId"
        (click)="assignUser()">
        Assign
      </ion-button>
    </div>
  </div>
</div>