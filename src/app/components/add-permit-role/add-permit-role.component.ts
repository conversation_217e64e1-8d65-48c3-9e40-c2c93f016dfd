import { Component, OnInit } from '@angular/core';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { ModalController } from '@ionic/angular';
import { PERMIT_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-add-permit-role',
  templateUrl: './add-permit-role.component.html',
  styleUrls: ['./add-permit-role.component.scss'],
})
export class AddPermitRoleComponent implements OnInit {
  public permit: PERMIT_HEADER;
  public usersList: any[] = [];
  public segmentValue: string = 'Internal';
  public internalUsers: any[] = [];
  public externalUsers: any[] = [];
  public filteredInternalUsers: any[] = [];
  public filteredExternalUsers: any[] = [];
  public isExternal: boolean = false;
  public searchText: string = '';
  public selectedUserId: string = '';
  public selectedUser: any = null;
  constructor(
    private modalCtrl: ModalController,
    private unviredSDK: UnviredCordovaSDK,
    private dataService: DataService
  ) { }

  async ngOnInit(): Promise<void> {
    let getUsersQuery = '';
    if (this.permit.AGENT_ID_EXT != null && this.permit.AGENT_ID_INT != null) {
      getUsersQuery = `select B.first_name, B.last_name, B.USER_ID, B.role_name, A.review, A.is_internal 
      from role_header as A join user_header as B on A.role_name=B.role_name 
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}','${this.permit.AGENT_ID_INT}')) 
        where review = 'true'`;
    } else if (
      this.permit.AGENT_ID_EXT != null &&
      this.permit.AGENT_ID_INT == null
    ) {
      getUsersQuery = `select B.first_name, B.last_name, B.USER_ID, B.role_name, A.review, A.is_internal 
      from role_header as A join user_header as B on A.role_name=B.role_name 
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}')) 
        where review = 'true'`;
    } else {
      getUsersQuery = `select B.first_name, B.last_name, B.USER_ID, B.role_name, A.review, A.is_internal 
      from role_header as A join user_header as B on A.role_name=B.role_name 
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_INT}')) 
        where review = 'true'`;
    }
    let fetchgetUsersQueryResult = await this.unviredSDK.dbExecuteStatement(
      getUsersQuery
    );
    if (fetchgetUsersQueryResult?.data?.length > 0) {
      this.usersList = fetchgetUsersQueryResult?.data;
      console.log("this.usersList", this.usersList);
    }


    if (this.permit.AGENT_ID_EXT != null) {
      this.internalUsers = this.usersList.filter(user => user.IS_INTERNAL == "true");
      this.externalUsers = this.usersList.filter(user => user.IS_INTERNAL == "false");
      this.isExternal = true;
    } else {
      this.internalUsers = this.usersList.filter(user => user.IS_INTERNAL == "true");
    }

    // Initialize filtered arrays
    this.filteredInternalUsers = [...this.internalUsers];
    this.filteredExternalUsers = [...this.externalUsers];
  }
  cancel() {
    return this.modalCtrl.dismiss(null, 'cancel');
  }

  selectUser(user: any) {
    this.selectedUserId = user.USER_ID;
    this.selectedUser = user;
  }

  assignUser() {
    if (this.selectedUser) {
      return this.modalCtrl.dismiss(this.selectedUser);
    }
  }

  filterUsers() {
    const searchTerm = this.searchText.toLowerCase().trim();

    if (!searchTerm) {
      this.filteredInternalUsers = [...this.internalUsers];
      this.filteredExternalUsers = [...this.externalUsers];
    } else {
      this.filteredInternalUsers = this.internalUsers.filter(user =>
        `${user.FIRST_NAME} ${user.LAST_NAME}`.toLowerCase().includes(searchTerm)
      );
      this.filteredExternalUsers = this.externalUsers.filter(user =>
        `${user.FIRST_NAME} ${user.LAST_NAME}`.toLowerCase().includes(searchTerm)
      );
    }
  }
}
