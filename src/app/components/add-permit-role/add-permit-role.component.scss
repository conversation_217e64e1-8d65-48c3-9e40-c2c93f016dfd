// Modal Header Styling
.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

// Modal Container Layout
.modal-container {
  display: flex;
  flex-direction: column;
  height: 75vh; // Increased from 70vh to 75vh to show one more row
  min-height: 450px; // Increased minimum height slightly
  max-height: 550px; // Increased max height to accommodate one more row
  background: #f8f9fa;
}

// Ensure the component takes full height in modal context
:host {
  display: block;
  height: 100%;
}

// Fixed Header Section
.fixed-header {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;
}

// Scrollable Content Section
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  background: white;
  min-height: 0; // Important for flex scrolling
}

// Fixed Footer Section
.fixed-footer {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e0e0e0;
  z-index: 10;
  position: relative; // Ensure it's positioned correctly
}

// Custom Segment Tabs
.segment-container {
  padding: 0 20px 0; // Removed top padding completely
}

.segment-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.segment-tab {
  flex: 1;
  text-align: center;
  padding: 12px 16px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;

  span {
    font-size: 16px;
    font-weight: 500;
    color: #999;
    transition: color 0.3s ease;
  }

  &.active {
    span {
      color: #6366f1;
      font-weight: 600;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: #6366f1;
    }
  }
}

// Search Container
.search-container {
  padding: 0 20px; // Removed all vertical padding
}

.custom-searchbar {
  --background: #f5f5f5;
  --border-radius: 12px;
  --box-shadow: none;
  --placeholder-color: #999;
  --color: #333;
  --padding-top: 0;
  --padding-bottom: 0;
  margin: 0; // Remove any default margins

  .searchbar-input {
    font-size: 16px;
  }
}

// User List Container
.user-list-container {
  padding: 0; // Removed all padding
}

// User Item Styling
.user-item {
  display: flex;
  align-items: center;
  padding: 8px 20px; // Increased back to 8px for better readability
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;

  &:hover {
    background: #f8f9fa;
  }

  &.selected {
    background: #e8e5ff;
    border-left: 4px solid #6366f1;

    .user-avatar {
      background: #6366f1;

      ion-icon {
        color: white;
      }
    }
  }

  &:last-child {
    border-bottom: none;
  }
}

.user-avatar {
  width: 32px; // Reduced from 40px to 32px
  height: 32px; // Reduced from 40px to 32px
  border-radius: 50%;
  background: #e3f2fd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px; // Reduced from 16px to 12px
  transition: all 0.2s ease;

  ion-icon {
    font-size: 16px; // Reduced from 20px to 16px
    color: #1976d2;
  }
}

.user-info {
  flex: 1;

  .user-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
  }
}

// Action Buttons
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px; // Slightly reduced padding
  gap: 12px;
  min-height: 76px; // Ensure minimum height for visibility

  ion-button {
    flex: 1;
    height: 44px;
    font-weight: 600;

    &[fill="clear"] {
      --color: #666;
      border: 1px solid #ddd;
      --border-radius: 8px;
    }

    &[fill="solid"] {
      --background: #6366f1;
      --border-radius: 8px;

      &[disabled] {
        --background: #ccc;
        --color: #999;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .modal-container {
    height: 85vh; // Increased from 80vh to 85vh for mobile
    max-height: 650px; // Increased max height for mobile
  }

  .segment-tab span {
    font-size: 14px;
  }

  .user-item {
    padding: 6px 16px; // Slightly more padding for mobile touch targets
  }

  .user-avatar {
    width: 28px; // Smaller for mobile
    height: 28px;
    margin-right: 10px;

    ion-icon {
      font-size: 14px;
    }
  }

  .user-name {
    font-size: 15px;
  }

  .search-container {
    padding: 0 16px; // Removed all vertical padding for mobile
  }

  .segment-container {
    padding: 0 16px 0; // Removed top padding for mobile
  }
}