<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>Fill Form</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()">
        <ion-icon slot="icon-only" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


  <div class="content-area">

      <div id="formio" [ngClass]="{'isDisable': isReport}"></div>
      <p *ngIf="!isFormFound" class="empty-data-info" style="text-align: center; color: #666; margin-top: 50px;">
        No Forms to Fill!
      </p>
    
  </div>

<!-- 
<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="cancel()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" (click)="saveForm()">Save</ion-button>
  </ion-toolbar>
</ion-footer>
 -->





