import { CommonModule } from '@angular/common';
import { Component, OnInit  , Input, CUSTOM_ELEMENTS_SCHEMA, Inject, ChangeDetectorRef} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController, IonicModule, LoadingController, ModalController } from '@ionic/angular';
import * as moment from 'moment';
import { DOCUMENT_ATTACHMENT, DOCUMENT_HEADER, USER_DOC_HEADER, USER_SKILL_HEADER } from 'src/app/data-models/data_classes';
import { AttachmentsService } from 'src/app/services/attachments.service';
import imageCompression from 'browser-image-compression';

import { DataService } from 'src/app/services/data.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-add-skill',
  templateUrl: './add-skill.component.html',
  styleUrls: ['./add-skill.component.scss'],
  standalone: true,
  imports: [IonicModule , CommonModule , FormsModule , ReactiveFormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AddSkillComponent  implements OnInit {

  @Input() skillList: any
  @Input() user: any
  @Input() userSkills: any[]
  @Input() isEditMode: any
  @Input()  filteredUserDocs: any
  showLinkForm: boolean = false
  selectedFile: any;
  filteredSkillList: any[]
   userDocs = [];
   fileArray: File[]
   skillForm: FormGroup;
   selectedFiles: File[] = [];
   skillsData: any
   newlyaddedItems = [];
   errorMessage = ''

   combinedList: any[] = [];
   @Input() selectedSkill: any;
   userDoc: USER_DOC_HEADER = {
     USER_ID: '',
     DOC_ID: '',
     DOC_CTX: undefined,
     P_MODE: '',
     LID: '',
     FID: '',
     OBJECT_STATUS: 0,
     SYNC_STATUS: 0
   }


   showInputs = false;

   // Create FormGroup with two FormControls
   linkForm = new FormGroup({
     linkname: new FormControl('',),
     linkurl: new FormControl('',) // Validate URL
   });

   links: { name: string; url: string }[] = [];
  saveData: boolean = false;



  constructor( private modalController: ModalController, private dataService: DataService ,    private loader: BusyIndicatorService,
     private unviredSDK: UnviredCordovaSDK , public  attachmentService: AttachmentsService , public alertController: AlertController,
     private modalCtrl: ModalController, public sanitizer: DomSanitizer,    public loadingController: LoadingController,  private translate: TranslateService,
     private fb: FormBuilder,
     ) {
      this.skillForm = this.fb.group({
        skillType: new FormControl('', Validators.required),
        rating: new FormControl(0),
        linkname: new FormControl(''),
        linkurl: new FormControl('') // Validate URL
      });

     }

  ngOnInit() {
    console.log('AddSkillComponent initialized');

    // Reset all arrays and properties to ensure clean state for each modal opening
    this.combinedList = [];
    this.userDocs = [];
    this.newlyaddedItems = [];
    this.links = []; // Reset links array
    this.fileArray = []; // Reset file array
    this.selectedFiles = []; // Reset selected files
    this.showLinkForm = false; // Reset link form visibility
    this.showInputs = false; // Reset inputs visibility
    this.errorMessage = ''; // Reset error message

    // Reset form controls
    this.linkForm.reset();

    // Initialize filteredSkillList
    if (!this.filteredSkillList) {
      this.filteredSkillList = [];
    }

    if(this.isEditMode){
      if (this.selectedSkill) {
        console.log('Edit mode with selected skill:', this.selectedSkill);
        this.skillForm.patchValue({
          skillType: this.selectedSkill.SKILL_TYPE,
          rating: this.selectedSkill.RATING
        });

        // Safely get filtered docs
        if (this.filteredUserDocs && Array.isArray(this.filteredUserDocs)) {
          try {
            const filteredDocs = this.getFilteredDocs(this.selectedSkill.SKILL_TYPE);
            console.log('Filtered docs:', filteredDocs);

            // Add documents to combined list
            if (filteredDocs && filteredDocs.length > 0) {
              filteredDocs.forEach(doc => {
                if (doc && doc.docItem && doc.docItem.FILE_NAME) {
                  this.combinedList.push({
                    type: 'certificate',
                    name: doc.docItem.FILE_NAME
                  });
                }
              });
            }

            // Add links to combined list
            if (this.links && this.links.length > 0) {
              this.links.forEach(link => {
                if (link && link.name && link.url) {
                  this.combinedList.push({
                    type: 'link',
                    name: link.name,
                    url: link.url
                  });
                }
              });
            }
          } catch (error) {
            console.error('Error setting up combined list:', error);
          }
        }
      }
    } else {
      console.log('Add mode');
      // Set up filtered skill list for add mode
      if (this.skillList && Array.isArray(this.skillList)) {
        if (!this.userSkills || this.userSkills.length === 0) {
          this.filteredSkillList = this.skillList;
        } else if (this.userSkills.length > 0) {
          this.filteredSkillList = this.skillList.filter(skill =>
            !this.userSkills.some(userSkill =>
              userSkill && skill && skill.SKILL_HEADER &&
              userSkill.SKILL_TYPE === skill.SKILL_HEADER.SKILL_TYPE
            )
          );
        }
      }
    }

    console.log('Combined list:', this.combinedList);
    console.log('Filtered skill list:', this.filteredSkillList);
  }




  async cancel() {
    console.log('Closing skill modal...');
    return this.modalController.dismiss(this.skillForm);
  }



  async onFileSelected(event: any) {
    const input = event.target as HTMLInputElement;

    if (input.files && input.files.length > 0) {
      this.fileArray = Array.from(input.files);
    }

    const files = event.target.files;
    this.newlyaddedItems = []; // Reset newly added items before adding new ones

    for (let file of files) {
      const newItem = { type: 'certificate', name: file.name, file: file };

      this.combinedList.push(newItem);
      this.newlyaddedItems.push(newItem); // Track only new files
    }

    console.log('Newly added certificates:', this.newlyaddedItems);
    if(this.newlyaddedItems.length > 0 ){
      // this.saveSkillsCertificate()
    }
    if (this.fileArray?.length > 0) {
      this.fileArray.forEach(async (file) => {
        await this.attachmentService
          .uploadFileToServer(file)
          .then(async (result) => {
            console.log("result in upload",result)
            let documetAttachmentHeader = this.createDocumentAttachmentHeader(
              result.attachmentId,
              file
            );
            let documetHeader = await this.createDocumentHeader(
              result.attachmentId,
              file,
              'A'
            );

            let documetHeaderInput = {
              DOCUMENT: [
                {
                  DOCUMENT_HEADER: documetHeader,
                  DOCUMENT_ATTACHMENT: [documetAttachmentHeader],
                },
              ],
            };

            let updateDocumentResponse: any =
              await this.attachmentService.uploadAttachment(documetHeaderInput);
            if (updateDocumentResponse.type == ResultType.success) {

              // Save inspection doc
              let uDoc = new USER_DOC_HEADER();
              uDoc.USER_ID = this.user.USER_ID;
              uDoc.DOC_ID = result.attachmentId;
              uDoc.DOC_CTX = JSON.stringify({ skillType: this.skillForm.value.skillType });
              uDoc.P_MODE = 'A';
              uDoc.OBJECT_STATUS = 1;
              uDoc.FID = this.user.LID
              await this.unviredSDK.dbInsert(
                'USER_DOC',
                uDoc,
                false
              );
              this.userDocs.push(uDoc)
              console.log("before adding new doc", this.filteredUserDocs)
              await this.getUserDocsInUi()
              console.log("on adding new doc", this.filteredUserDocs)
              // await this.saveSkill()
            }
          }
          )
      })
    }

  }



  saveSkillsCertificate(){
    if (this.skillForm.valid) {
      const skillData = {
        SKILL_TYPE: this.skillForm.value.skillType,
        RATING: this.skillForm.value.rating,
        FILES: this.selectedFiles
      };
    }

    if(this.fileArray == undefined){

      this.saveSkill();
    }else if(this.fileArray.length > 0 ){
      this.fileArray.forEach(async (file) => {
        await this.attachmentService
        .uploadFileToServer(file)
        .then(async (result) => {
          console.log(result)
          let documetAttachmentHeader = this.createDocumentAttachmentHeader(
            result.attachmentId,
            file
          );
          let documetHeader = await this.createDocumentHeader(
            result.attachmentId,
            file,
            'A'
          );

          let documetHeaderInput = {
            DOCUMENT: [
              {
                DOCUMENT_HEADER: documetHeader,
                DOCUMENT_ATTACHMENT: [documetAttachmentHeader],
              },
            ],
          };

          let updateDocumentResponse: any =
            await this.attachmentService.uploadAttachment(documetHeaderInput);
           if (updateDocumentResponse.type == ResultType.success) {

                      // Save inspection doc
                      this.userDoc = new USER_DOC_HEADER();
                      this.userDoc.USER_ID  = this.user.USER_ID;
                      this.userDoc.DOC_ID = result.attachmentId;
                      this.userDoc.DOC_CTX = JSON.stringify({ skillType : this.skillForm.value.skillType});
                      this.userDoc.P_MODE = 'A';
                      this.userDoc.OBJECT_STATUS = 1;
                      this.userDoc.FID = this.user.LID
                      await this.unviredSDK.dbInsert(
                        'USER_DOC',
                        this.userDoc,
                        false
                      );
                      this.userDocs.push(this.userDoc)
                      console.log("before adding new doc" , this.filteredUserDocs)
                      await this.getUserDocsInUi()
                      console.log("on adding new doc" , this.filteredUserDocs)
                      // await this.saveSkill()
                    }
        }
      )
    })
    }

    // this.processNewlyAddedItems()
  }






 createDocumentAttachmentHeader(attachmentUid: string, file: any): any {
    var documentAttachmentHeader = <DOCUMENT_ATTACHMENT>{};
    documentAttachmentHeader.UID = attachmentUid;
    documentAttachmentHeader.FILE_NAME = file.name;
    documentAttachmentHeader.MIME_TYPE = file.type;
    documentAttachmentHeader.ATTACHMENT_STATUS = 'UPLOADED';
    return documentAttachmentHeader;
  }

  async createDocumentHeader(
      attachmentUid: string,
      file: any,
      mode: string
    ): Promise<any> {
      var documentHeader = <DOCUMENT_HEADER>{};
      documentHeader.DOC_ID = attachmentUid;
      documentHeader.FILE_NAME = file.name;
      if(file.type == 'text/uri-list'){
        documentHeader.TITLE =  file.urlName   ;
      }else {
        documentHeader.TITLE =  `${this.selectedSkill} - CERTIFICATE`   ;
      }

      documentHeader.CREATED_BY = '';
      documentHeader.CREATED_ON = moment().valueOf();
      documentHeader.P_MODE = mode;
      documentHeader.MIME_TYPE = file.type;
      documentHeader.DOC_TYPE = this.getFileExtension(file?.type);


      // Fetch user id from user role table.
      let userResult = await this.unviredSDK.dbExecuteStatement(
        `SELECT * FROM USER_ROLE`
      );
      if (userResult.type == ResultType.success) {
        if (userResult.data && userResult.data.length > 0) {
          documentHeader.CREATED_BY = userResult.data[0].USER_ID;
        }
      }
      return documentHeader;
    }



    getFileExtension(mimeType) {
      const mimeToExtMap = {
        'image/jpeg': 'IMAGE',
        'image/png': 'IMAGE',
        'application/pdf': 'PDF',
        'text/plain': 'TEXT',
        'text/uri-list': 'URL',
        'application/msword': 'WORD',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCUMENT',
        'application/vnd.ms-excel': 'EXCEL',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'EXCEL',
        'application/vnd.ms-powerpoint': 'PPT',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PPT',
        'text/csv': 'CSV'
      };
      return mimeToExtMap[mimeType] || 'DOCUMENT';
    }


  // This method is replaced by the async saveLink() method below

  async saveSkill() {

    this.saveData = true;
    if (this.userSkills == undefined) {
      this.userSkills = []
    }


    const skillToUpdate = this.userSkills.find(skill => skill.SKILL_TYPE === this.skillForm.value.skillType);
    if (skillToUpdate) {
      skillToUpdate.RATING = this.skillForm.value.rating.toString();
    } else {
      const skilltypeHeader = {
        P_MODE: "A",
        USER_ID: this.user.USER_ID,
        SKILL_TYPE: this.skillForm.value.skillType,
        RATING: this.skillForm.value.rating.toString(),
        OBJECT_STATUS : 1,
       FID : this.user.LID
      };

      this.userSkills.push(skilltypeHeader);
      // let res = await this.unviredSDK.dbInsertOrUpdate(
      //   'USER_SKILL',
      //   skilltypeHeader,
      //   false
      // );
    }
    console.log(this.userSkills)
console.log("this.user docs " , this.userDocs)
    let temp = []
    // if (this.userSkills?.length > 0) {
    //   for (let i = 0; i < this.userSkills.length; i++) {
    //     let skilltypeHeader = {} as USER_SKILL_HEADER;
    //     skilltypeHeader.P_MODE = 'A';
    //     skilltypeHeader.USER_ID = this.userSkills[i].USER_ID;
    //     skilltypeHeader.SKILL_TYPE = this.userSkills[i].SKILL_TYPE;
    //     skilltypeHeader.RATING = this.userSkills[i].RATING;
    //     skilltypeHeader.OBJECT_STATUS = 1;
    //     skilltypeHeader.FID = this.user.LID

    //     let res = await this.unviredSDK.dbInsertOrUpdate(
    //       'USER_SKILL',
    //       skilltypeHeader,
    //       false
    //     );
    //   }
    // }

    console.log('Dismissing modal after saving skill...');
    return this.modalController.dismiss({ userSkills: this.userSkills, userDocs: this.userDocs });
  }






    updateRating(newRating: number) {
      this.skillForm.patchValue({ rating: newRating });
    }

    // This method is replaced by the removeItem method below


    getFilteredDocs(skillType: string ) {
      // Check if parameters are valid
      if (!skillType) {
        console.error("Invalid skillType parameter", skillType);
        return [];
      }

      // Check if filteredUserDocs is defined
      if (!this.filteredUserDocs || !Array.isArray(this.filteredUserDocs)) {
        console.error("filteredUserDocs is not an array", this.filteredUserDocs);
        return [];
      }

      try {
        return this.filteredUserDocs.filter(image => {
          if (!image || !image.docItem || !image.docItem.DOC_CTX) {
            return false;
          }

          try {
            let docCtx = JSON.parse(image.docItem.DOC_CTX);
            return docCtx && docCtx.skillType === skillType;
          } catch (e) {
            console.error("Error parsing DOC_CTX:", e);
            return false;
          }
        });
      } catch (error) {
        console.error("Error in getFilteredDocs:", error);
        return [];
      }
    }

    getDocumentIcon(docType: string): string {
      if (!docType) return 'document-outline';

      switch(docType.toUpperCase()) {
        case 'EXCEL':
          return 'document-text-outline';
        case 'PPT':
          return 'document-text-outline';
        case 'WORD':
        case 'DOCUMENT':
          return 'document-text-outline';
        case 'PDF':
          return 'document-text-outline';
        case 'TEXT':
          return 'document-text-outline';
        case 'CSV':
          return 'document-text-outline';
        case 'URL':
          return 'link-outline';
        case 'IMAGE':
          return 'image-outline';
        default:
          return 'document-outline';
      }
    }

    // This method is replaced by the async downloadFile method below



    async deleteSelectedCertificate(j: any , index: number){
      console.log("file to delete is ",this.filteredUserDocs[j])
      let deletefile = this.filteredUserDocs[j]
      let deleteDoc = []
      const alert = await this.alertController.create({
        header: 'Delete',
        message: 'Are you sure you want to delete this certificate' + '?',
        buttons: [
          {
            text: 'No',
            role: 'cancel',
            cssClass: 'secondary',
            handler: (blah) => { },
          },
          {
            text: 'Yes',
            role: 'confirm',
            handler: async () => {

              if(!this.isEditMode){
                this.removeItem(j)
              } else{
                let userDocHeader = {} as USER_DOC_HEADER;
                userDocHeader.P_MODE = 'D';
                userDocHeader.USER_ID = deletefile.docItem.USER_ID
                userDocHeader.DOC_ID = deletefile.docItem.DOC_ID
                userDocHeader.DOC_CTX = deletefile.docItem.DOC_TYPE
                userDocHeader.OBJECT_STATUS = 2;
                userDocHeader.FID = this.user.LID
                console.log("userdocheader " , userDocHeader)
                this.userDocs.push(userDocHeader)
                console.log("filteredUserDocs" , this.filteredUserDocs)
                this.filteredUserDocs.splice(j, 1);
                console.log("filteredUserDocs" , this.filteredUserDocs)
             await this.unviredSDK.dbInsertOrUpdate(
                'USER_DOC',
                 userDocHeader,
                 false
              );

              }


            }
          },
        ],
      });
      await alert.present();
    }



    toggleInput() {
      this.showInputs = !this.showInputs;
    }





    async saveLink() {
      const linkName = this.linkForm.value.linkname;
      const linkUrl = this.linkForm.value.linkurl;

      if (linkUrl) {
        const newLink = {
          type: 'text/uri-list',
          urlName: linkName,
          name: linkUrl.startsWith('http') ? linkUrl : 'https://' + linkUrl
        };

        this.combinedList.push(newLink);
        this.newlyaddedItems.push(newLink); // Track newly added links
        console.log("the combined list is " , this.combinedList)

        this.linkForm.reset();
        this.showLinkForm = false;

        if (newLink.type === 'text/uri-list') {
          console.log('Uploading URL:', newLink.name);
              let guid = this.unviredSDK.guid().replace(/-/g, '');
              let attachmentId = guid
              let documentHeader = await this.createDocumentHeader(attachmentId, newLink, 'A');
              let documentHeaderInput = {
                DOCUMENT: [{ DOCUMENT_HEADER: documentHeader }],
              };
              let updateDocumentResponse: any = await this.attachmentService.uploadAttachment(documentHeaderInput);
              if (updateDocumentResponse.type === ResultType.success) {
                this.userDoc = new USER_DOC_HEADER();
                this.userDoc.USER_ID = this.user.USER_ID;
                this.userDoc.DOC_ID = attachmentId;
                this.userDoc.DOC_CTX = JSON.stringify({ skillType: this.skillForm.value.skillType });
                this.userDoc.P_MODE = 'A';
                this.userDoc.OBJECT_STATUS = 1;
                this.userDoc.FID = this.user.LID;
                await this.unviredSDK.dbInsert('USER_DOC', this.userDoc, false);
              console.log("the userdocs after is " , this.userDocs , this.filteredUserDocs)
              await this.getUserDocsInUi()
              }
            }
          }
    }

    removeItem(index: number) {
      this.combinedList.splice(index, 1);
    }


    async getUserDocsInUi(){

      console.log("the userdocs is " , this.filteredUserDocs)
      let userId : string= this.user.USER_ID
      let userDocQUERY = `SELECT * FROM document_header dh JOIN user_doc ud ON dh.DOC_ID = ud.DOC_ID WHERE ud.USER_ID ='${userId}'`;
      // let userDocQUERY = `SELECT * FROM document_header dh JOIN user_doc ud ON dh.DOC_ID = ud.DOC_ID WHERE ud.USER_ID = '${userId}' AND ud.DOC_ID IS NOT NULL`;

      let userDocResult = await this.unviredSDK.dbExecuteStatement(userDocQUERY);
      console.log("userDocResult" , userDocResult)

      const filteredDocIds = new Set(this.filteredUserDocs.map(doc => doc.docItem.DOC_ID));
      let newData =  userDocResult.data.filter(doc => !filteredDocIds.has(doc.DOC_ID));
      if(newData.length > 0){
            for (let ri = 0; ri < newData.length; ri++) {
          if (
            newData[ri].THUMBNAIL &&
            newData[ri].THUMBNAIL != null
          ) {
            if (
              newData[ri].THUMBNAIL.includes(
                'data:image/png;base64,'
              ) ||
              newData[ri].THUMBNAIL.includes(
                'data:image/jpeg;base64,'
              ) ||
              newData[ri].THUMBNAIL.includes(
                'data:image/jpg;base64,'
              )
            ) {
            } else {
              newData[ri].THUMBNAIL =
                this.sanitizer.bypassSecurityTrustResourceUrl(
                  `data:image/jpg;base64,${userDocResult.data[ri].THUMBNAIL}`
                );
            }
            this.filteredUserDocs.push({
              thumbnail: newData[ri].THUMBNAIL,
              file: null,
              docItem: newData[ri],
              DOC_TYPE: newData[ri].DOC_TYPE
            });
          } else {
             this.filteredUserDocs.push({
               thumbnail: newData[ri].THUMBNAIL,
             file: null,
              docItem: newData[ri],
             DOC_TYPE: newData[ri].DOC_TYPE
             });
          }
        }

        console.log("this.filteredUserDocs is" , this.filteredUserDocs)
      }

    }



    async downloadFile(item: any) {
      let responseData: any;
      let response: any;
      let documentData: any;
      await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
      if (item.file === null) {
        documentData = {
          DOCUMENT: [
            {
              DOCUMENT_HEADER: {
                DOC_ID: item.docItem.DOC_ID,
              },
            },
          ],
        };

        if (this.dataService.getDevicePlatform() == 'browser') {
          response = await this.dataService.getDocument(documentData);
          if (response?.code && response.code === 404) {
            this.unviredSDK.logError(
              'InspectionDetailsComponent',
              'imageInFullscreen',
              'Error while Loading Image.'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
            responseData =
              await this.attachmentService.downloadAndWriteAttachmentToFile(
                response.DOCUMENT_ATTACHMENT[0],
                item.docItem.DOC_ID
              );

            const blob = new Blob([responseData.body], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = item.docItem.DOC_ID + '.' + this.getDownloadFileExtension(item.DOC_TYPE);
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
          }
        }
      } else {
        const blob = new Blob([item.file], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = this.unviredSDK.guid().replace(/-/g, '').slice(0, 8); + '.' + this.getDownloadFileExtension(item.DOC_TYPE);
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
      }

      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }



    getDownloadFileExtension(mimeType) {
      const mimeToExtMap = {
        'PDF': 'pdf',
        'EXCEL': 'xlsx',
        'TEXT':'txt',
        'WORD': 'docx',
        'DOCUMENT': 'docx',
        'PPT': 'pptx',
        'CSV': 'csv'
      };
      return mimeToExtMap[mimeType] || '.pdf';
    }

    // Method to open files in browser tab instead of downloading
    async downloadFileAndOpen(item: any) {
      let responseData: any;
      let response: any;
      let documentData: any;

      await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');

      if (item.file === null) {
        documentData = {
          DOCUMENT: [
            {
              DOCUMENT_HEADER: {
                DOC_ID: item.docItem.DOC_ID,
              },
            },
          ],
        };

        if (this.dataService.getDevicePlatform() == 'browser') {
          response = await this.dataService.getDocument(documentData);

          if (response?.code && response.code === 404) {
            console.error('Error: Document not found.');
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
            return;
          }

          if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
            responseData = await this.attachmentService.downloadAndWriteAttachmentToFile(
              response.DOCUMENT_ATTACHMENT[0],
              item.docItem.DOC_ID
            );

            if (responseData && responseData.body) {
              // Create blob and open in new tab instead of downloading
              const mimeType = this.getMimeTypeFromDocType(item.DOC_TYPE);
              const blob = new Blob([responseData.body], { type: mimeType });
              const url = window.URL.createObjectURL(blob);
              window.open(url, '_blank');

              // Clean up the URL after a delay
              setTimeout(() => {
                window.URL.revokeObjectURL(url);
              }, 1000);
            }
          }
        }
      } else {
        // Handle files that are already loaded
        const mimeType = this.getMimeTypeFromDocType(item.DOC_TYPE);
        const blob = new Blob([item.file], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank');

        // Clean up the URL after a delay
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 1000);
      }

      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }

    // Helper method to get proper MIME type from DOC_TYPE
    getMimeTypeFromDocType(docType: string): string {
      const mimeTypeMap = {
        'PDF': 'application/pdf',
        'EXCEL': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'WORD': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'DOCUMENT': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'PPT': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'TEXT': 'text/plain',
        'CSV': 'text/csv'
      };

      return mimeTypeMap[docType] || 'application/octet-stream';
    }








}
