ion-toolbar {
  --color: white;
}

ion-card {
  margin: 0 0 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

/* Tab container styling */
.tab-container {
  padding: 0;
  margin: 0;
  width: 100%;
  background-color: white;
  height: 36px; /* Match the height of the segment buttons */
  display: flex;
  align-items: center;
  overflow: hidden;
}

/* Master data tabs styling */
.master-data-tabs {
  background-color: white;
  margin: 20px 15px 0;
  width: calc(100% - 30px);
  overflow-x: auto;
  overflow-y: hidden; /* Prevent vertical scrolling */
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  padding: 0;
  height: 36px; /* Match the height of the segment buttons */
  display: flex;
  align-items: center;
  --background: white;
  justify-content: stretch;
  --width: 100%;
}

ion-segment-button {
  --color: #6c757d;
  --color-checked: #ffffff;
  --indicator-color: transparent;
  --background: white;
  --background-checked: #3880ff;
  --border-radius: 0;
  --border-color: transparent;
  --border-style: none;
  --border-width: 0;
  --padding-top: 0; /* Remove vertical padding */
  --padding-bottom: 0; /* Remove vertical padding */
  min-width: 120px;
  font-size: 14px;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0;
  transition: all 0.3s ease;
  margin: 0;
  overflow: hidden;
  height: 36px; /* Explicitly set height (reduced by ~30% from default) */
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1; /* Make buttons stretch to fill available space */
}

/* Fix for segment button to ensure full background color */
:host ::ng-deep ion-segment-button::part(indicator-background) {
  height: 100%;
  top: 0;
  bottom: 0;
}

/* Ensure selected tab text is white */
:host ::ng-deep ion-segment-button.segment-button-checked {
  --color: #ffffff !important;
  color: #ffffff !important;
}

:host ::ng-deep ion-segment-button.segment-button-checked ion-label {
  color: #ffffff !important;
}

/* Fix for ion-label in segment buttons */
:host ::ng-deep ion-segment-button ion-label {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* Full width segment buttons */
.segment-button-full-width {
  flex: 1;
  width: 100%;
  min-width: 50%;
}

/* Tab content styling */
.tab-content {
  padding: 0;
  margin-top: -1px; /* Remove gap between tabs and content */
  background-color: white;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin-left: 15px;
  margin-right: 15px;
  margin-bottom: 20px;
  width: calc(100% - 30px);
  overflow: hidden;
  border-top: 1px solid #e9ecef; /* Add a subtle border to connect with tabs */
}

/* Approval badges styling */
.approval-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.approval-badge {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.approval-badge ion-icon {
  margin-left: 5px;
  font-size: 14px;
  cursor: pointer;
}

/* Form layout */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-width: 100%;
  padding: 15px;
}

.form-row {
  display: flex;
  gap: 20px;
  width: 100%;
  margin-bottom: 5px;
}

.form-group {
  flex: 1;
  min-width: 0;
}

/* Validation styling */
.validation-error {
  color: var(--ion-color-danger);
  font-size: 12px;
  margin-left: 16px;
  margin-bottom: 8px;
}

:host ::ng-deep ion-input.ion-invalid {
  --border-color: var(--ion-color-danger) !important;
}

:host ::ng-deep ion-input.ion-valid {
  --border-color: var(--ion-color-success) !important;
}

/* Floating label styling */
ion-item {
  --border-color: #ced4da;
  --border-radius: 4px;
  --background: transparent;
  margin-bottom: 5px;
}

ion-label {
  font-weight: 500;
  color: #00629b !important;
}

.error {
  text-align: center;
  color: #d63232;
  margin-top: 10px;
}

/* Approvals section */
.approvals-section {
  margin-top: 5px;
}

.facility-divisions-div {
  max-height: 35vh;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  overflow-y: auto;
  width: 100%;
}

.margin-top-10 {
  margin-top: 0;
}

.ion-list-item {
  --min-height: 0px;
  height: 35px;
}

.selected {
  --background: rgba(63, 81, 181, 0.15);
  color: #3880ff;
}

.selected>i {
  color: white;
  font-weight: 540;
}

.sub-title-p-tag {
  padding-left: 8px;
  padding-top: 8px;
  margin-bottom: 1rem;
  color: #00629b;
  font-weight: 500;
}



.addOrUpdateFacility .modal-wrapper {
  --width: 45% !important;
  --height: 65% !important;
}

.refreshButton {
  --border-color: #868686 !important;
  --border-width: 1px !important;
  --border-radius: 8px !important;
  font-weight: 600 !important;
  margin-right: 10px;
}

/* Skills tab styling */
.skills-tab-content {
  padding: 0;
}

/* Search card styling */
.search-card {
  background-color: white;
  border-radius: 0;
  box-shadow: none; /* Remove shadow to blend with tab content */
  padding: 12px 16px;
  margin: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  border-bottom: 1px solid #e9ecef; /* Add subtle border for separation */
}

.search-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0 12px;
  flex: 1;
  max-width: 400px;
}

.search-wrapper input {
  border: none;
  background: transparent;
  padding: 10px;
  width: 100%;
  font-size: 14px;
  color: #495057;
  outline: none;
}

.search-wrapper ion-icon {
  color: #adb5bd;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #e9ecef;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #3880ff;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-btn:hover {
  background-color: #3171e0;
}

.action-btn ion-icon, .add-btn ion-icon {
  font-size: 18px;
}

.custom-table {
  width: 100%;
  background: white;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  border: none;
  margin: 0;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 500;
  padding: 0 15px;
  border-radius: 0;
  height: 32px;
  align-items: center;
  font-size: 13px;
}

.header-col {
  padding: 0 10px;
}

.table-row {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 36px;
  font-size: 13px;
}

.table-col {
  padding: 0 0px;
}

.skill-col {
  flex: 0 0 25%;
}

.rating-col {
  flex: 0 0 25%;
}

.cert-col {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cert-content {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.row-actions {
  display: flex;
  align-items: center;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  margin-left: 10px;
  min-width: 80px;
  justify-content: flex-end;
}

.row-actions ion-button {
  --padding-start: 4px;
  --padding-end: 4px;
  height: 30px;
  margin: 0;
}

.skill-card:hover .row-actions {
  opacity: 1;
}

.skill-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.certificate-item {
  margin-bottom: 1px;
}

.doc-link {
  display: flex;
  align-items: center;
  color: #3880ff;
  text-decoration: none;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.doc-link ion-icon {
  margin-right: 5px;
  font-size: 18px;
}

.skill-card {
  margin: 0;
  padding: 10px;
  border-radius: 0;
  border-bottom: 1px solid #e0e0e0;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: none;
  --background: transparent;
}

.star-icon {
  font-size: 16px;
  color: gold;
  margin-right: 2px;
}

.rating-display {
  display: flex;
  align-items: center;
}

.unselected-card {
  background-color: white;
  color: #333;
  border-left: 3px solid transparent;
}

/* Empty skills message styling moved to global.scss */

/* Content container styling */
.content-container {
  --padding-top: 0px;
  --padding-bottom: 10px;
  --padding-start: 10px;
  --padding-end: 10px;
  --overflow: auto;
  --background: transparent;
}

/* Tab content container styling */
.details-tab-content, .skills-tab-content {
  padding: 0;
  height: auto;
  max-height: 460px;
  overflow-y: auto;
}

@media (max-height: 600px) {
  .facility-divisions-div {
    max-height: 150px;
  }
}

