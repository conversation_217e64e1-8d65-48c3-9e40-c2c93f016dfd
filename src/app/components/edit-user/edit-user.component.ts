import { ChangeDetectorRef, Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>er,
  Mo<PERSON><PERSON>ontroller,
  LoadingController,
  InfiniteScrollCustomEvent,
} from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { DOCUMENT_HEADER, SKILL_HEADER, USER_APPROVAL_TYPE, USER_DOC_HEADER, USER_HEADER, USER_SKILL_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { AddSkillComponent } from '../add-skill/add-skill.component';
import { DomSanitizer } from '@angular/platform-browser';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { AttachmentsService } from 'src/app/services/attachments.service';
import { ImageFullScreenComponent } from '../image-full-screen/image-full-screen.component';
import * as markerjs2 from 'markerjs2';
@Component({
  selector: 'app-edit-user',
  templateUrl: './edit-user.component.html',
  styleUrls: ['./edit-user.component.scss'],
})
export class EditUserComponent implements OnInit {
  @Input() user: USER_HEADER;
  @Input() agentId: any;
  @Input() roles: any;
  public selectedRole: any;
  public isUpdatingContent: boolean = false;
  public pageHeading: string;
  public displayError: string = '';
  public userapprovals: any[] = [];
  public approvalsList: any[] = [];
  public defaultSelectedApprovals: any[] = [];
  public skillsList: any[] = []
  public userSkill: any[] = []; // Initialize as empty array to prevent undefined errors

  public hoveredIndex: number

  segmentValue: string = 'Details'

  public userDocs : any =  []
  editableIndex: number | null = null;
  displayedApprovals = []; // The approvals to display
  itemsPerPage = 7; // Number of items to load per page
  currentIndex = 0;
  userDocsData : any[] = []; // Initialize as empty array
  selectedIndex: number | null = null; // Track selected card index
  selectedSkillCard: any = null; // Store selected card data
  public progressbar: boolean = false; // Progress bar indicator
  public selectedApprovals: string[] = []; // Selected approval types

  // Validation flags
  public emailValid: boolean = true;
  public phoneValid: boolean = true;
  public emailErrorMessage: string = '';
  public phoneErrorMessage: string = '';
  public displayPhone: string = ''; // For UI display with formatting
  public emailExists: boolean = false; // Flag to track if email already exists

  // Skills search
  public skillSearchTerm: string = '';
  public filteredSkills: any[] = [];


  constructor(
    public modalCtrl: ModalController,
    private dataService: DataService,
    public alertController: AlertController,
    public translate: TranslateService,
    private loadingController: LoadingController,
    private unviredSDK: UnviredCordovaSDK,
    public sanitizer: DomSanitizer,
    private loader: BusyIndicatorService,
    public attachmentService: AttachmentsService,
    public modalController : ModalController,
    public cdr: ChangeDetectorRef
  ) {

   }

  ngOnChanges(changes: SimpleChanges) {
    // Handle changes to the roles input property
    if (changes['roles'] && changes['roles'].currentValue) {
      console.log('Roles changed in EditUserComponent:', this.roles);
      console.log('Roles array length:', this.roles?.length);
      console.log('First role structure:', this.roles?.[0]);
      // Do not set default role - require user to explicitly select one for security
      console.log('Roles loaded but no default role set - user must select explicitly');
    }
  }

  async ngOnInit() {
    console.log('EditUserComponent ngOnInit - roles:', this.roles);
    console.log('EditUserComponent ngOnInit - user:', this.user);

    // Initialize user object first if it's null to prevent errors in getApprovalTypes
    if (!this.user) {
      this.user = new USER_HEADER();
      // Initialize all required properties to prevent null/undefined errors
      this.user.USER_ID = '';
      this.user.FIRST_NAME = '';
      this.user.LAST_NAME = '';
      this.user.EMAIL = '';
      this.user.PHONE = '';
      this.user.ROLE_NAME = '';
      this.user.IS_INTERNAL = '';
      this.user.P_MODE = 'A';
      this.user.OBJECT_STATUS = 1;

      this.pageHeading = 'Add User';
      // Do not default to any role - require user to explicitly select one
      this.selectedRole = null;
      console.log('No default role set - user must select a role explicitly');
    } else if (Object.keys(this.user).length > 0) {
      this.isUpdatingContent = true;
      this.pageHeading = 'Update User';
      this.selectedRole = this.user.ROLE_NAME;

      // Initialize displayPhone for existing users
      if (this.user.PHONE) {
        this.formatPhoneForDisplay(this.user.PHONE);
      }
    }

    await this.getApprovalTypes();
    await this.getSkills(); // Load skills for both add and edit modes to get descriptions

    if (this.isUpdatingContent) {
      // await this.getUserApprovalsByUserId();
      await this.getUserSkillAndApprovalTypes();
      await this.getUserDocsInUi()
      console.log("this.userSkill" , this.userSkill)
    }

    this.loadInitialItems();
  }

  async ionViewWillEnter() {
    console.log("the userskills list is " , this.userSkill)
    await this.getSkills();
    await this.getUserSkillAndApprovalTypes();
  }

  async getApprovalTypes() {
    // Get all approvals first
    const allApprovals = await this.dataService.getData('APPROVAL_TYPE_HEADER', null, 'APPR_TYPE ASC');

    // Get the agent's IS_INTERNAL property to determine filtering
    let isAgentInternal = false;
    if (this.agentId) {
      try {
        const agentData = await this.dataService.getData('AGENT_HEADER', `AGENT_ID = '${this.agentId}'`);
        if (agentData && agentData.length > 0) {
          isAgentInternal = agentData[0].IS_INTERNAL === 'true';
        }
      } catch (error) {
        console.error('Error fetching agent data:', error);
      }
    }

    // Filter approvals based on agent's internal/external status
    this.approvalsList = allApprovals.filter(approval => {
      if (approval.SCOPE === 'B') {
        // Include if scope is 'B' (Both) - always show
        return true;
      } else if (isAgentInternal && approval.SCOPE === 'I') {
        // Include if agent is internal and scope is 'I' (Internal)
        return true;
      } else if (!isAgentInternal && approval.SCOPE === 'E') {
        // Include if agent is external and scope is 'E' (External)
        return true;
      }
      return false;
    });

    // If selectedApprovals is already populated, update the checks
    if (this.selectedApprovals && this.selectedApprovals.length > 0) {
      this.updateApprovalChecks();
    }

  }

  async getUserSkillAndApprovalTypes() {
    if (!this.user) return;

    if(this.user.P_MODE == null){
      this.user.P_MODE = 'M'
    }

    let customData = {
      USER: [
        {
          USER_HEADER: { USER_ID: this.user.USER_ID },
        },
      ],
    };

    // this.userapprovals = await this.dataService.getData('USER_APPROVAL_TYPE', `USER_ID='${this.user.USER_ID}'`, 'APPR_TYPE ASC');
    await this.dataService.getUserById(customData).then((res: any) =>{
      console.log("res is getUserSKILL" , res)
      console.log("Full response data:", JSON.stringify(res.data, null, 2))
      this.userDocs = res
      if(res.type == ResultType.success ){
        console.log("result in get sk and ap is " , res.data.USER[0])

        // Get the user data object
        const userData = res.data.USER[0];
        console.log("userData structure:", userData);

        let apprResult = userData.USER_APPROVAL_TYPE;
        let skillRes = userData.USER_SKILL;

        console.log("skillres is " , skillRes)
        console.log("apprResult (raw):" , apprResult)
        console.log("apprResult type:", typeof apprResult)
        console.log("apprResult length:", apprResult?.length)

        this.userSkill = skillRes || [];
        this.filteredSkills = [...(skillRes || [])]; // Initialize filtered skills

        // Process approval types and populate selectedApprovals
        if (apprResult && Array.isArray(apprResult) && apprResult.length > 0) {
          this.selectedApprovals = apprResult.map((approval: any) => approval.APPR_TYPE);
          console.log("selectedApprovals populated with:", this.selectedApprovals);
          // Update the approval checks to reflect the selected approvals
          setTimeout(() => {
            this.updateApprovalChecks();
          }, 100); // Small delay to ensure approvalsList is loaded
        } else {
          this.selectedApprovals = [];
          console.log("No approval types found for user - apprResult:", apprResult);
        }
        this.userapprovals = apprResult || [];
      }
    })


    if (this.userapprovals?.length > 0) {
      // Initialize selectedApprovals array
      this.selectedApprovals = [];

      for (let ff = 0; ff < this.userapprovals?.length; ff++) {
        for (let ff1 = 0; ff1 < this.approvalsList.length; ff1++) {
          if (this.userapprovals[ff].APPR_TYPE == this.approvalsList[ff1].APPR_TYPE) {
            this.approvalsList[ff1].isChecked = true;
            this.defaultSelectedApprovals.push(this.approvalsList[ff1]);
            // Add to selectedApprovals array for multi-select
            this.selectedApprovals.push(this.approvalsList[ff1].APPR_TYPE);
          }
        }
      }
    }

    if(this.userSkill?.length>0){

    }
  }

  updateApprovalChecks() {
    console.log("updateApprovalChecks called");
    console.log("approvalsList:", this.approvalsList);
    console.log("selectedApprovals:", this.selectedApprovals);

    // Safety check to ensure approvalsList is loaded
    if (!this.approvalsList || this.approvalsList.length === 0) {
      console.log("approvalsList not loaded yet, skipping updateApprovalChecks");
      return;
    }

    // Reset all approval checks
    this.approvalsList.forEach(approval => {
      approval.isChecked = false;
    });

    // Set isChecked based on selectedApprovals
    if (this.selectedApprovals && this.selectedApprovals.length > 0) {
      this.selectedApprovals.forEach(selectedApprType => {
        const approval = this.approvalsList.find(a => a.APPR_TYPE === selectedApprType);
        console.log(`Looking for approval type: ${selectedApprType}, found:`, approval);
        if (approval) {
          approval.isChecked = true;
          console.log(`Set ${selectedApprType} as checked`);
        } else {
          console.log(`Approval type ${selectedApprType} not found in approvalsList`);
        }
      });
      console.log("Updated approval checks for:", this.selectedApprovals);
      console.log("Final approvalsList state:", this.approvalsList.map(a => ({APPR_TYPE: a.APPR_TYPE, isChecked: a.isChecked})));
    }
  }

  removeApproval(approvalType: string) {
    // Remove the approval from the selectedApprovals array
    this.selectedApprovals = this.selectedApprovals.filter(a => a !== approvalType);

    // Update the approval checks
    this.updateApprovalChecks();
  }

  // Helper function to get approval description from approval type code
  getApprovalDescription(approvalType: string): string {
    const approval = this.approvalsList.find(a => a.APPR_TYPE === approvalType);
    return approval ? approval.DESCRIPTION : approvalType;
  }

  // Helper function to get skill description from skill type code
  getSkillDescription(skillType: string): string {
    // Check if skillsList has the skill data with descriptions
    if (this.skillsList && this.skillsList.length > 0) {
      const skill = this.skillsList.find(s => s.SKILL_TYPE === skillType);
      return skill ? skill.DESCRIPTION || skillType : skillType;
    }
    return skillType;
  }

  filterSkills() {
    // Check if userSkill is defined
    if (!this.userSkill) {
      this.filteredSkills = [];
      return;
    }

    if (!this.skillSearchTerm || this.skillSearchTerm.trim() === '') {
      this.filteredSkills = [...this.userSkill];
    } else {
      const searchTerm = this.skillSearchTerm.toLowerCase().trim();
      this.filteredSkills = this.userSkill.filter(skill => {
        // Search by skill code (original functionality)
        const skillCodeMatch = skill.SKILL_TYPE.toLowerCase().includes(searchTerm);

        // Search by skill description
        const skillDescription = this.getSkillDescription(skill.SKILL_TYPE);
        const skillDescMatch = skillDescription.toLowerCase().includes(searchTerm);

        // Search by certificate file names
        const certificates = this.getFilteredDocs(skill.SKILL_TYPE);
        const certificateMatch = certificates.some(cert => {
          if (cert?.docItem?.FILE_NAME) {
            return cert.docItem.FILE_NAME.toLowerCase().includes(searchTerm);
          }
          return false;
        });

        // Return true if any of the criteria match
        return skillCodeMatch || skillDescMatch || certificateMatch;
      });
    }

    // Reset selection when filtering
    this.selectedIndex = null;
    this.selectedSkillCard = null;
  }

  // Clear skill search term and reset filtered data
  clearSkillSearch() {
    this.skillSearchTerm = '';
    this.filterSkills();
  }

  clearError() {
    this.displayError = '';
  }

  extractAllInfoMessages(result: any): string {
    // Extract all InfoMessage categories including WARNING, ERROR, FAILURE, etc.
    let allMessages = '';
    console.log('extractAllInfoMessages - Full result:', JSON.stringify(result, null, 2));

    if (result && result.data && result.data.InfoMessage && result.data.InfoMessage.length > 0) {
      console.log('extractAllInfoMessages - InfoMessage array:', result.data.InfoMessage);
      for (let info of result.data.InfoMessage) {
        console.log('extractAllInfoMessages - Processing info:', info);
        // Include all message categories except SUCCESS
        if (info.category && info.category !== 'SUCCESS') {
          if (allMessages) allMessages += '\n';
          // Prefix message with category for clarity
          allMessages += `${info.category}: ${info.message}`;
          console.log('extractAllInfoMessages - Added message:', `${info.category}: ${info.message}`);
        }
      }
    } else {
      console.log('extractAllInfoMessages - No InfoMessage found or invalid structure');
    }

    console.log('extractAllInfoMessages - Final result:', allMessages);
    return allMessages;
  }



  formatPhoneForDisplay(phoneNumber: string) {
    // Format a raw phone number for display
    const digits = phoneNumber.replace(/\D/g, ''); // Remove all non-digits

    if (digits.length <= 3) {
      this.displayPhone = digits;
    } else if (digits.length <= 6) {
      this.displayPhone = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    } else {
      this.displayPhone = `${digits.slice(0, 3)}-${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
    }
  }

  formatPhoneNumber(event: any) {
    if (!this.user) return;

    const input = event.target.value.replace(/\D/g, ''); // Remove all non-digits
    let formatted = '';

    if (input.length <= 3) {
      formatted = input;
    } else if (input.length <= 6) {
      formatted = `${input.slice(0, 3)}-${input.slice(3)}`;
    } else {
      formatted = `${input.slice(0, 3)}-${input.slice(3, 6)}-${input.slice(6, 10)}`;
    }

    // Store raw digits in user.PHONE (for server)
    this.user.PHONE = input.slice(0, 10); // Only digits, max 10 characters

    // Store formatted version for display
    this.displayPhone = formatted;

    // Validate phone number
    this.validatePhoneNumber();

    this.cdr.detectChanges();
  }

  async onEmailInput() {
    // Auto-copy email to USER_ID field for new users
    if (!this.isUpdatingContent && this.user) {
      this.user.USER_ID = this.user.EMAIL || '';
      this.cdr.detectChanges();
    }

    // Validate email format and check for duplicates
    await this.validateEmail();
  }

  async validateEmail() {
    // Regular expression for email validation
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    if (!this.user || !this.user.EMAIL) {
      this.emailValid = false;
      this.emailErrorMessage = 'Email is required';
      this.emailExists = false;
    } else if (!emailRegex.test(this.user.EMAIL)) {
      this.emailValid = false;
      this.emailErrorMessage = 'Please enter a valid email address';
      this.emailExists = false;
    } else {
      this.emailValid = true;
      this.emailErrorMessage = '';

      // Check for existing email only for new users
      if (!this.isUpdatingContent) {
        await this.checkEmailExists();
      } else {
        this.emailExists = false;
      }
    }

    return this.emailValid && !this.emailExists;
  }

  async checkEmailExists() {
    try {
      // Query the local database to check if email already exists
      const existingUsers = await this.dataService.getData('USER_HEADER', `EMAIL = '${this.user.EMAIL}'`);

      if (existingUsers && existingUsers.length > 0) {
        this.emailExists = true;
      } else {
        this.emailExists = false;
      }
    } catch (error) {
      console.error('Error checking email existence:', error);
      this.emailExists = false;
    }
  }

  validatePhoneNumber() {
    // Check if phone number has exactly 10 digits (raw format)
    const phoneRegex = /^\d{10}$/;

    if (!this.user || !this.user.PHONE) {
      this.phoneValid = false;
      this.phoneErrorMessage = 'Phone number is required';
    } else if (!phoneRegex.test(this.user.PHONE)) {
      this.phoneValid = false;
      this.phoneErrorMessage = 'Phone number must be exactly 10 digits';
    } else {
      this.phoneValid = true;
      this.phoneErrorMessage = '';
    }

    return this.phoneValid;
  }

  async getUserApprovalsByUserId() {
    let customData = {
      USER: [
        {
          USER_APPROVAL_TYPE: {
            USER_ID: this.user.USER_ID,
          },
        },
      ],
    };

    let res = await this.dataService.getAgentUser(customData);
  }

  async getSkills() {
    // Load all skills to get descriptions for display
    this.skillsList = await this.dataService.getData('SKILL_HEADER', null, 'SKILL_TYPE ASC');
    console.log("the skillsList in get skills is " , this.skillsList)
  }


// selectCard method removed as row selection is no longer needed



  async getUsersServerData() {
    let userAgent = await this.dataService.getData('USER_AGENT');
    let customData = {
      AGENT_USER: [
        {
          AGENT_USER_HEADER: {
            AGENT_ID: userAgent[0].AGENT_ID,
            USER_ID: '',
            P_MODE: '',
          },
        },
      ],
    };

    let res = await this.dataService.getAgentUser(customData);
  }

  async addOrUpdateUser(user: any) {
      console.log("the userskill after delete is " , this.skillsList , this.isUpdatingContent , user)
      this.displayError = '';

      // Validate email and phone before proceeding
      const isEmailValid = await this.validateEmail();
      const isPhoneValid = this.validatePhoneNumber();

      if (!isEmailValid || !isPhoneValid || this.emailExists) {
        if (this.emailExists) {
          this.displayError = 'User with same email already exists. Please use a different email address.';
        } else {
          this.displayError = 'Please correct the validation errors before saving.';
        }
        return;
      }

      this.progressbar = true;
      let result: any;

      this.user.P_MODE = this.isUpdatingContent ? 'M' : 'A';
      this.user.OBJECT_STATUS = this.isUpdatingContent ? 2 : 1;
      this.user.ROLE_NAME = this.selectedRole;
      let selectedApprovs: any= [];
      let updatedSkills : any = []

      // Save approvals based on the selectedApprovals array
      selectedApprovs = await this.saveApprovalData()
      console.log("selectedApprovs" , selectedApprovs)

      console.log("user skill before save is",this.userSkill)
      if(this.userSkill?.length > 0 ){
       updatedSkills = await this.updateSkillData(this.userSkill)
       console.log("selectedSkills" , updatedSkills)
      }

      // this.unviredSDK.dbExportWebData()
      if (!this.isUpdatingContent) {
        this.user['USER_APPROVAL_TYPE'] = selectedApprovs
        this.user['USER_SKILL'] = updatedSkills
        let customData = {
          AGENT: this.agentId,
          USERS: [


              this.user


          ],


        };

        await this.displayPleaseWaitLoader(
          this.translate.instant('Please wait, User data is being added')
        );
        console.log("the new user with skills and appr" , customData)
        result = await this.dataService.createAgentUsers(customData);
        console.log('user add response at users modal', result);
        console.log('result type:', typeof result);
        console.log('result === undefined:', result === undefined);
        console.log('result === null:', result === null);

        // Handle the API response properly
        let resultInfoMsg = this.dataService.handleInfoMessage(result);
        let customInfoMsg = this.extractAllInfoMessages(result);

        console.log('API Response result:', result);
        console.log('handleInfoMessage result:', resultInfoMsg);
        console.log('Custom info messages:', customInfoMsg);

        console.log('Checking result.type:', result?.type);
        console.log('ResultType.success:', ResultType.success);
        console.log('result.type == ResultType.success:', result?.type == ResultType.success);

        if (result && result.type == ResultType.success) {
          await this.dataService.getAllUserData();
          // Prioritize custom info messages (with category prefixes) over standard info messages
          if (customInfoMsg && customInfoMsg.length > 0) {
            // Show custom extracted messages including WARNING, FAILURE, etc.
            await this.loadingController.dismiss();
            this.progressbar = false;
            this.displayError = customInfoMsg;
            console.log('Custom extracted messages:', customInfoMsg);
          } else if (resultInfoMsg && resultInfoMsg?.length > 0) {
            // Show info message from server in error ribbon (could be WARNING, ERROR, etc.)
            await this.loadingController.dismiss();
            this.progressbar = false;
            this.displayError = resultInfoMsg;
            console.log('Server info message:', resultInfoMsg);
          } else {
            // Success without info message
            await this.loadingController.dismiss();
            this.progressbar = false;
            await this.modalController.dismiss({ status: true });
          }
        } else {
          // Handle error cases
          await this.loadingController.dismiss();
          this.progressbar = false;
          if (result && result.message && result.message.length > 0) {
            this.displayError = result.message;
          } else if (result && result.error && result.error.length > 0) {
            this.displayError = result.error;
          } else if (result == undefined) {
            this.displayError = 'User not added - No response from server';
          } else {
            this.displayError = 'Error occurred while adding user. Please try again.';
          }
        }


      } else {

        let customData =

            {
              USER_HEADER: this.user,
              // USER_APPROVAL_TYPE: selectedApprovs,
              // USER_SKILL: updatedSkills
            }

        ;
        console.log("custom data for approval types " , customData)
        await this.displayPleaseWaitLoader(
          this.translate.instant('Please wait, Users data is being updated')
        );

        console.log("whole user data " , customData)

        result = await this.dataService.modifyUserDetailsRequest(
          customData,
          this.isUpdatingContent
        );

        let dbSelectQuery1 = `SELECT * FROM USER_APPROVAL_TYPE WHERE USER_ID = '${this.user.USER_ID}' ;`
        let res2 = await this.unviredSDK.dbExecuteStatement(dbSelectQuery1)

        if(res2.type == ResultType.success){
          console.log("res2 data" , res2.data)

        }


        let customData1 =

        {
          USER_HEADER: user,
          // USER_APPROVAL_TYPE: selectedApprovs,
          // USER_SKILL: updatedSkills
        }



    ;
    console.log("custom data for approval types " , customData1)
        this.dataService.getUserById(customData1)
        // let dbSelectQuery = `SELECT * FROM USER_APPROVAL_TYPE WHERE USER_ID = '${this.user.USER_ID}' ;`
        // let res1 = await this.unviredSDK.dbExecuteStatement(dbSelectQuery)

        // if(res1.type == ResultType.success){
        //   console.log("res1 data" , res1.data)
        // }
        let resultInfoMsg = this.dataService.handleInfoMessage(result);
        let customInfoMsg = this.extractAllInfoMessages(result);

        console.log('Update API Response result:', result);
        console.log('Update handleInfoMessage result:', resultInfoMsg);
        console.log('Update custom info messages:', customInfoMsg);

        if (result.type == ResultType.success) {
          await this.dataService.getAllUserData()
          // this.unviredSDK.dbExportWebData()
          // Prioritize custom info messages (with category prefixes) over standard info messages
          if (customInfoMsg && customInfoMsg.length > 0) {
            // Show custom extracted messages including WARNING, FAILURE, etc.
            await this.loadingController.dismiss();
            this.progressbar = false;
            this.displayError = customInfoMsg;
            console.log('Custom extracted messages:', customInfoMsg);
          } else if (resultInfoMsg && resultInfoMsg?.length > 0) {
            // Show info message from server in error ribbon (could be WARNING, ERROR, etc.)
            await this.loadingController.dismiss();
            this.progressbar = false;
            this.displayError = resultInfoMsg;
            console.log('Server info message:', resultInfoMsg);
          } else {
            // Success without info message
            await this.loadingController.dismiss();
            this.progressbar = false;
            await this.modalController.dismiss({ status: true });
          }
        } else {
          // Handle error cases
          await this.loadingController.dismiss();
          this.progressbar = false;
          if (result && result.message && result.message.length > 0) {
            this.displayError = result.message;
          } else if (result && result.error && result.error.length > 0) {
            this.displayError = result.error;
          } else {
            this.displayError = 'Error occurred while updating user. Please try again.';
          }
        }

      }

  }

  // Display Loading dialog.
  async displayPleaseWaitLoader(messageReceived) {
    const loading = await this.loadingController.create({
      message: messageReceived,
      backdropDismiss: false,
    });
    await loading.present();
  }

  closeModal() {
    this.modalCtrl.dismiss();
  }


  async saveApprovalData(){
    let temp = [];
    let res = true;
    let customData1

    // Use the selectedApprovals array to get the selected approval types
    console.log("selected approvals in save approval" , this.selectedApprovals)
    if(this.selectedApprovals && this.selectedApprovals.length > 0){
      for (let f = 0; f < this.selectedApprovals.length; f++) {
        let userApprovalHeader = {} as USER_APPROVAL_TYPE;
        userApprovalHeader.P_MODE = 'A';
        userApprovalHeader.USER_ID = this.user.USER_ID;
        userApprovalHeader.APPR_TYPE = this.selectedApprovals[f];
        userApprovalHeader.OBJECT_STATUS = 1 ;
        userApprovalHeader.FID = this.user.LID;
        console.log("userApprovalHeader.FID", userApprovalHeader.FID , this.user.LID)
        temp.push(userApprovalHeader);

        let res = await this.unviredSDK.dbInsertOrUpdate(
          'USER_APPROVAL_TYPE',
          userApprovalHeader,
          false
        );

        console.log("user id is ",this.user.USER_ID)
        let dbSelectQuery = `SELECT * FROM USER_APPROVAL_TYPE WHERE USER_ID = '${this.user.USER_ID}' ;`
        let res1 = await this.unviredSDK.dbExecuteStatement(dbSelectQuery)

        if(res1.type == ResultType.success){
          console.log("res1 data" , res1.data)
        }
      }
    }

    console.log("tempList",temp)
    return temp
  }





  loadInitialItems() {

    this.displayedApprovals = this.approvalsList.slice(
      0,
      this.itemsPerPage
    );
    this.currentIndex = this.itemsPerPage;
  }

  loadMore(event: InfiniteScrollCustomEvent) {
    const nextItems = this.approvalsList.slice(
      this.currentIndex,
      this.currentIndex + this.itemsPerPage
    );

    this.displayedApprovals = this.displayedApprovals.concat(nextItems);
    this.currentIndex += this.itemsPerPage;

    // Complete the infinite scroll event
    setTimeout(() => {
      event.target.complete();

      // Disable infinite scroll if all items are loaded
      if (this.displayedApprovals.length === this.approvalsList.length) {
        event.target.disabled = true;
      }
    }, 500); // Simulate a delay for data loading
  }



  async openSkillModal(editMode: boolean, skill?: any) {
    try {
      console.log('Opening skill modal...');
      if (editMode) {
        // Handle opening the modal in edit mode with the existing skill
        this.selectedSkillCard = skill;  // Save the skill data that is being edited
      } else {
        // Handle opening the modal in add mode (if needed)
        this.selectedSkillCard = null;   // Ensure it's cleared for adding a new skill
      }

      console.log("this.skillsList , this.userSkill , selectedSkill" , this.skillsList , this.userSkill , skill);

      // Initialize userDocsData if it's undefined
      if (!this.userDocsData) {
        this.userDocsData = [];
      }

      const modal = await this.modalCtrl.create({
        component: AddSkillComponent,
        backdropDismiss: false,
        cssClass: 'add-update-modal',
        componentProps: {
          skillList: this.skillsList,
          user: this.user,
          userSkills: this.userSkill,
          isEditMode: editMode,
          selectedSkill: skill ? { ...skill } : null, // Send data if editing
          filteredUserDocs : this.userDocsData
        },
      });

      this.cdr.detectChanges();
      await modal.present();
      const { data } = await modal.onDidDismiss();

      console.log("Data on dismiss: ", data);

      if (data) {
        if (data.touched) {
          const updatedSkill = data.value; // The updated skill after modal closes

          console.log("updatedSkill" , updatedSkill)
          if (updatedSkill) {
            const index = this.userSkill.findIndex(skill => skill.SKILL_TYPE === updatedSkill.skillType);
            if (index !== -1) {
              this.userSkill[index].rating = updatedSkill.rating;
            }
          }
        }

        console.log(this.userSkill)

        // Replace the entire userSkill array with the updated one from the modal
        // This ensures new skills are added and existing skills are updated correctly
        if(data.userSkills) {
          this.userSkill = data.userSkills;
        }

        // Replace the entire userDocs array with the updated one from the modal
        if(data.userDocs) {
          this.userDocs = data.userDocs;
        }

        // Update skill if needed
        if (data.SKILL_TYPE) {
          const index = this.userSkill.findIndex(skill => skill.SKILL_TYPE === data.SKILL_TYPE);
          if (index !== -1) {
            this.userSkill[index] = data;
          }
        }

        // Refresh the filtered skills to update the UI immediately
        this.filterSkills();

        // Trigger change detection to ensure UI updates
        this.cdr.detectChanges();
      }
    } catch (error) {
      console.error('Error opening skill modal:', error);
    }
  }





  async deleteSelectedItem(item: any , index: number) {

    console.log("the selected item is " , item)
    const alert = await this.alertController.create({
      header: 'Delete',
      message: 'Are you sure you want to delete this item' + '?',
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Yes',
          role: 'confirm',
          handler: async () => {
            let matchedDocs = []

            try {
              // Check if userDocs has the expected structure
              if (this.userDocs && this.userDocs.data && this.userDocs.data.USER &&
                  this.userDocs.data.USER[0] && this.userDocs.data.USER[0].USER_DOC) {

                const userDocsArray = this.userDocs.data.USER[0].USER_DOC;

                if(userDocsArray && userDocsArray.length > 0){
                  // Find all matching docs
                  userDocsArray.forEach(doc => {
                    try {
                      const docCtx = JSON.parse(doc.DOC_CTX);
                      if(docCtx && docCtx.skillType === item.SKILL_TYPE) {
                        matchedDocs.push(doc);
                      }
                    } catch (e) {
                      console.error("Error parsing DOC_CTX:", e);
                    }
                  });
                }
              }
            } catch (error) {
              console.error("Error processing user docs:", error);
            }


            if (matchedDocs.length > 0) {

              for (let i = 0; i < matchedDocs.length; i++) {
                let userDocHeader = {} as USER_DOC_HEADER;
                userDocHeader.P_MODE = 'D';
                userDocHeader.USER_ID = matchedDocs[i].USER_ID
                userDocHeader.DOC_ID = matchedDocs[i].DOC_ID
                userDocHeader.DOC_CTX = matchedDocs[i].DOC_TYPE
                userDocHeader.OBJECT_STATUS = 2;
                userDocHeader.FID = this.user.LID
                // console.log("userdocheader " , userDocHeader)

                await this.unviredSDK.dbInsertOrUpdate(
                  'USER_DOC',
                  userDocHeader,
                  false
                );

              }




            } else {
              console.log("No matching document found.");
            }



            this.userSkill = this.userSkill.filter(skill => skill.SKILL_TYPE !== item.SKILL_TYPE);

            console.log("this.userskjill after delete " , this.userSkill)
          // for(let i =0 ; i < this.userSkill.length ; i++){
          //   let userSkill = new USER_SKILL_HEADER();
          //   userSkill.USER_ID = this.userSkill[i].USER_ID;
          //   userSkill.SKILL_TYPE = this.userSkill[i].SKILL_TYPE;
          //   userSkill.RATING = this.userSkill[i].RATING;
          //   userSkill.OBJECT_STATUS = 1;
          //   userSkill.P_MODE = "A"
          //   userSkill.FID = this.user.LID;


          //   let res = await this.unviredSDK.dbInsertOrUpdate(
          //     'USER_SKILL',
          //     userSkill,
          //     false
          //   );

          // }



          console.log("user skill in delete is " , this.userSkill)
          this.selectedIndex = null;

          // Refresh the filtered skills to update the UI immediately
          this.filterSkills();

          // Trigger change detection to ensure UI updates
          this.cdr.detectChanges();


          },
        },
      ],
    });
    await alert.present();
  }


  async updateSkillData(updatedSkillList){

    let temp = [];

    if(updatedSkillList.length > 0){
      for (let f = 0; f < updatedSkillList.length; f++) {
        let skilltypeHeader = {} as USER_SKILL_HEADER;
        skilltypeHeader.P_MODE = 'A';
        skilltypeHeader.USER_ID = this.user.USER_ID;
        skilltypeHeader.SKILL_TYPE = updatedSkillList[f].SKILL_TYPE;
        skilltypeHeader.RATING = updatedSkillList[f].RATING;
        skilltypeHeader.OBJECT_STATUS = 2 ;
        skilltypeHeader.FID =  this.user.LID;
          temp.push(skilltypeHeader);

          let res = await this.unviredSDK.dbInsertOrUpdate(
            'USER_SKILL',
            skilltypeHeader,
            false
          );


      }
    }
    return temp


  }



    async getUserDocsInUi(){
      try {
        console.log("the userdocs is " , this.userDocs)

        // Initialize userDocsData if it's undefined
        if (!this.userDocsData) {
          this.userDocsData = [];
        }

        // Check if userDocs has the expected structure
        if (!this.userDocs || !this.userDocs.data || !this.userDocs.data.USER ||
            !this.userDocs.data.USER[0] || !this.userDocs.data.USER[0].USER_HEADER) {
          console.error("userDocs does not have the expected structure");
          return;
        }

        let userId : string = this.userDocs.data.USER[0].USER_HEADER.USER_ID;
        if (!userId) {
          console.error("User ID is undefined");
          return;
        }

        let userDocQUERY = `SELECT * FROM document_header dh JOIN user_doc ud ON dh.DOC_ID = ud.DOC_ID WHERE ud.USER_ID ='${userId}'`;

        let userDocResult = await this.unviredSDK.dbExecuteStatement(userDocQUERY);
        console.log("userDocResult" , userDocResult)

        if(userDocResult?.data?.length > 0){
          for (let ri = 0; ri < userDocResult.data.length; ri++) {
            if (userDocResult.data[ri].THUMBNAIL && userDocResult.data[ri].THUMBNAIL != null) {
              if (userDocResult.data[ri].THUMBNAIL.includes('data:image/png;base64,') ||
                  userDocResult.data[ri].THUMBNAIL.includes('data:image/jpeg;base64,') ||
                  userDocResult.data[ri].THUMBNAIL.includes('data:image/jpg;base64,')) {
                // Already in base64 format
              } else {
                userDocResult.data[ri].THUMBNAIL = this.sanitizer.bypassSecurityTrustResourceUrl(
                  `data:image/jpg;base64,${userDocResult.data[ri].THUMBNAIL}`
                );
              }
            }

            this.userDocsData.push({
              thumbnail: userDocResult.data[ri].THUMBNAIL,
              file: null,
              docItem: userDocResult.data[ri],
              DOC_TYPE: userDocResult.data[ri].DOC_TYPE
            });
          }
        }
      } catch (error) {
        console.error("Error in getUserDocsInUi:", error);
        // Initialize userDocsData as empty array if there was an error
        this.userDocsData = [];
      }
    }



    async downloadFile(item: any) {
       let responseData: any;
       let response: any;
       let documentData: any;
       await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
       if (item.file === null) {
         documentData = {
           DOCUMENT: [
             {
               DOCUMENT_HEADER: {
                 DOC_ID: item.docItem.DOC_ID,
               },
             },
           ],
         };

         if (this.dataService.getDevicePlatform() == 'browser') {
           response = await this.dataService.getDocument(documentData);
           if (response?.code && response.code === 404) {
             this.unviredSDK.logError(
               'InspectionDetailsComponent',
               'imageInFullscreen',
               'Error while Loading Image.'
             );
             if (this.loader.isLoading) {
               await this.loader.dismissBusyIndicator();
             }
           }
           if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
             responseData =
               await this.attachmentService.downloadAndWriteAttachmentToFile(
                 response.DOCUMENT_ATTACHMENT[0],
                 item.docItem.DOC_ID
               );

             const blob = new Blob([responseData.body], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
             const url = window.URL.createObjectURL(blob);
             const a = document.createElement('a');
             a.href = url;
             a.download = item.docItem.DOC_ID + '.' + this.getDownloadFileExtension(item.DOC_TYPE);
             document.body.appendChild(a);
             a.click();
             window.URL.revokeObjectURL(url);
             document.body.removeChild(a);
           }
         }
       } else {
         const blob = new Blob([item.file], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
             const url = window.URL.createObjectURL(blob);
             const a = document.createElement('a');
             a.href = url;
             a.download = this.unviredSDK.guid().replace(/-/g, '').slice(0, 8); + '.' + this.getDownloadFileExtension(item.DOC_TYPE);
             document.body.appendChild(a);
             a.click();
             window.URL.revokeObjectURL(url);
             document.body.removeChild(a);
       }

       if (this.loader.isLoading) {
         await this.loader.dismissBusyIndicator();
       }
     }



      getDownloadFileExtension(mimeType) {
        const mimeToExtMap = {
          'PDF': 'pdf',
          'EXCEL': 'xlsx',
          'TEXT':'txt',
          'WORD': 'docx',
          'DOCUMENT': 'docx',
          'PPT': 'pptx',
          'CSV': 'csv'
        };
        return mimeToExtMap[mimeType] || '.pdf';
      }


       findIndex(items: any, item: any) {
          let array = items,
            result = array.findIndex(function (object) {
              return object.thumbnail === item.thumbnail;
            });
          return result;
        }


         async imageInFullscreen(item: any, fileName: any, index) {
            let responseData: any;
            let downloadedImage: any;
            // let fileName: any = "";
            let base64data: any;
            let response: any;
            let documentData: any;
            await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
            if (item.file === null) {
              documentData = {
                DOCUMENT: [
                  {
                    DOCUMENT_HEADER: {
                      DOC_ID: item.docItem.DOC_ID,
                    },
                  },
                ],
              };

              // Fetch and display newly added image if has P_Mode A.
              if (item.docItem.P_MODE == 'A') {
                downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
                  item.docItem.THUMBNAIL
                );
                // fileName = item.docItem.DOC_ID;
              } else {
                // For Browser
                if (this.dataService.getDevicePlatform() == 'browser') {
                  response = await this.dataService.getDocument(documentData);
                  if (response?.code && response.code === 404) {
                    this.unviredSDK.logError(
                      'InspectionDetailsComponent',
                      'imageInFullscreen',
                      'Error while Loading Image.'
                    );
                    if (this.loader.isLoading) {
                      await this.loader.dismissBusyIndicator();
                    }
                  }
                  if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
                    responseData =
                      await this.attachmentService.downloadAndWriteAttachmentToFile(
                        response.DOCUMENT_ATTACHMENT[0],
                        item.docItem.DOC_ID
                      );

                    var reader = new FileReader();
                    reader.readAsDataURL(responseData.body);
                    reader.onload = async (_event) => {
                      base64data = reader.result;
                      // console.log(base64data);
                      downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
                        `${base64data}`
                      );
                      // fileName = item.docItem.DOC_ID;
                    };
                  }
                } else {
                  // Fetch, download and display image if network present.
                  // if (this.network.type != 'unknown' && this.network.type != 'none') {
                  // For Mobile
                  let fetchDocumentAttachmentQuery = `select * from document_attachment where fid IN (select LID from document_header where doc_id = '${item.docItem.DOC_ID}')`;
                  let fetchDocumentAttachmentQueryResult: any =
                    await this.unviredSDK.dbExecuteStatement(
                      fetchDocumentAttachmentQuery
                    );
                  if (
                    fetchDocumentAttachmentQueryResult &&
                    fetchDocumentAttachmentQueryResult.data.length > 0
                  ) {
                    // fileName = fetchDocumentAttachmentQueryResult.data[0].FILE_NAME;
                    downloadedImage = this.attachmentService.normalizeURL(
                      'file://' +
                      fetchDocumentAttachmentQueryResult.data[0].LOCAL_PATH
                    );
                  } else {
                    // Download and fetch attachment form doc attachments table and display image.
                    response = await this.dataService.getDocument(documentData);
                    if (response.DOCUMENT_ATTACHMENT[0] !== undefined) {
                      let fetchDocAttachmentQuery = `select * FROM DOCUMENT_ATTACHMENT WHERE UID = '${response.DOCUMENT_ATTACHMENT[0].UID}'`;
                      let fetchDocAttachmentQueryResult: any =
                        await this.unviredSDK.dbExecuteStatement(
                          fetchDocAttachmentQuery
                        );
                      if (
                        fetchDocAttachmentQueryResult &&
                        fetchDocAttachmentQueryResult.data.length > 0
                      ) {
                        let downloadedAttachment =
                          await this.unviredSDK.downloadAttachment(
                            'DOCUMENT_ATTACHMENT',
                            fetchDocAttachmentQueryResult.data[0]
                          );
                        if (downloadedAttachment.type == ResultType.success) {
                          if (downloadedAttachment.data.length > 0) {
                            await this.dataService.sleep(1000);
                            let fetchDocAttachmentQuery1 = `select * FROM DOCUMENT_ATTACHMENT WHERE UID = '${downloadedAttachment.data[0].UID}'`;
                            let fetchDocAttachmentQueryResult1: any =
                              await this.unviredSDK.dbExecuteStatement(
                                fetchDocAttachmentQuery1
                              );
                            if (
                              fetchDocAttachmentQueryResult1 &&
                              fetchDocAttachmentQueryResult1.data.length > 0
                            ) {
                              // fileName = fetchDocAttachmentQueryResult1.data[0].FILE_NAME;
                              downloadedImage = this.attachmentService.normalizeURL(
                                'file://' +
                                fetchDocAttachmentQueryResult1.data[0].LOCAL_PATH
                              );
                            }
                          }
                        }
                      }
                    }
                  }
                  // }
                  //  else {
                  //   // Fetch and display image from inspection doc thumbnail if network not present.
                  //   downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
                  //     item.docItem.THUMBNAIL
                  //   );
                  // fileName = item.docItem.DOC_ID;
                  // }
                }
              }
            } else {
              downloadedImage = item.thumbnail;
            }

            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
            await this.dataService.sleep(500);
            const modal = await this.modalController.create({
              cssClass: 'full-screen-modal',
              component: ImageFullScreenComponent,
              componentProps: {
                imagePath: downloadedImage,
                imageName: fileName + `${index + 1}`,
              },
            });
            await modal.present();
          }





        editImage(image: any , index: number) {
          // let img = document.getElementsByClassName("imagefull-display");
          // this.annotateImages(img[0]);
        }


         annotateImages(img) {
            // new markerjs2.Activator.addKey('MJS2-P013-M397-4100');
            // let markerArea = new markerjs2.MarkerArea(img);
            // markerArea.uiStyleSettings.toolbarBackgroundColor = '#303f9f';
            // markerArea.uiStyleSettings.toolbarBackgroundHoverColor = '#111f7e';
            // markerArea.addRenderEventListener((imgURL) => {
            //   this.image = imgURL;
            //   this.modalController.dismiss(this.image, '', 'annotateImage');
            // });
            // markerArea.addCloseEventListener(() => {
            //   this.modalController.dismiss(false, '', 'annotateImage');
            // });
            // markerArea.show();
          }



          getFilteredDocs(skillType: string ) {
            try {
              // Check if userDocsData is defined
              if (!this.userDocsData || !Array.isArray(this.userDocsData)) {
                console.error("userDocsData is not an array", this.userDocsData);
                return [];
              }

              return this.userDocsData.filter(image => {
                if (!image || !image.docItem || !image.docItem.DOC_CTX) {
                  return false;
                }

                try {
                  let docCtx = JSON.parse(image.docItem.DOC_CTX);
                  return docCtx && docCtx.skillType === skillType;
                } catch (e) {
                  console.error("Error parsing DOC_CTX:", e);
                  return false;
                }
              });
            } catch (error) {
              console.error("Error in getFilteredDocs:", error);
              return [];
            }
          }





          async downloadFileAndOpen(item: any) {
            let responseData: any;
            let response: any;
            let documentData: any;

            await this.loader.showBusyIndicator('Please wait...', 'crescent');

            documentData = {
              DOCUMENT: [
                {
                  DOCUMENT_HEADER: {
                    DOC_ID: item.docItem.DOC_ID,
                  },
                },
              ],
            };

            if (this.dataService.getDevicePlatform() == 'browser') {
              response = await this.dataService.getDocument(documentData);

              if (response?.code && response.code === 404) {
                console.error('Error: Document not found.');
                await this.loader.dismissBusyIndicator();
                return;
              }

              if (response?.DOCUMENT_ATTACHMENT[0]) {
                responseData =
                  await this.attachmentService.downloadAndWriteAttachmentToFile(
                    response.DOCUMENT_ATTACHMENT[0],
                    item.docItem.DOC_ID
                  );

                if (responseData && responseData.body) {
                  // Create blob and open in new tab instead of downloading
                  const mimeType = this.getMimeTypeFromDocType(item.DOC_TYPE);
                  const blob = new Blob([responseData.body], { type: mimeType });
                  const url = window.URL.createObjectURL(blob);
                  window.open(url, '_blank');

                  // Clean up the URL after a delay
                  setTimeout(() => {
                    window.URL.revokeObjectURL(url);
                  }, 1000);
                }
              }
            }

            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }

          // Helper method to get proper MIME type from DOC_TYPE
          getMimeTypeFromDocType(docType: string): string {
            const mimeTypeMap = {
              'PDF': 'application/pdf',
              'EXCEL': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              'WORD': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'DOCUMENT': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'PPT': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              'TEXT': 'text/plain',
              'CSV': 'text/csv'
            };

            return mimeTypeMap[docType] || 'application/octet-stream';
          }









}
