import { Component, OnInit, Input } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';

declare let CameraPreview: any;

@Component({
  selector: 'app-camera-modal',
  templateUrl: './camera-modal.component.html',
  styleUrls: ['./camera-modal.component.scss'],
})
export class CameraModalComponent implements OnInit {
  @Input() controlId: string;

  flashMode: string = 'off';
  currentZoom: any;
  MAX_ZOOM: any;
  isAndroidBrowser = false;

  constructor(
    private modalController: ModalController,
    private platform: Platform,
    private device: Device,
    private file: File,
    private androidPermissions: AndroidPermissions
  ) {}

  ngOnInit() {
    console.log('Camera modal controlId:', this.controlId);
    let isbrowser = (this.device.platform === 'browser') ? true : false;
    let isAndroid = this.platform.is('android');
    this.isAndroidBrowser = isAndroid && isbrowser;
  }

  async ionViewDidEnter() {
    await this.checkCameraPermissions();
    setTimeout(() => {
      this.launchCamera();
    }, 300);
  }

  async checkCameraPermissions() {
    if (this.platform.is('android')) {
      try {
        const permission = await this.androidPermissions.checkPermission(
          this.androidPermissions.PERMISSION.CAMERA
        );
        if (!permission.hasPermission) {
          await this.androidPermissions.requestPermission(
            this.androidPermissions.PERMISSION.CAMERA
          );
        }
      } catch (error) {
        console.log('Camera permission error:', error);
      }
    }
  }

  launchCamera() {
    const cameraPreviewOpts = {
      x: 0,
      y: 80,
      width: window.screen.width,
      height: window.screen.height - 200,
      camera: 'rear',
      tapPhoto: false,
      previewDrag: false,
      toBack: false,
      alpha: 1
    };

    CameraPreview.startCamera(cameraPreviewOpts,
      (res) => {
        console.log('Camera started successfully:', res);
      },
      (err) => {
        console.log('Camera start error:', err);
      }
    );
  }

  getMaxZoom() {
    CameraPreview.getMaxZoom((value) => {
      this.MAX_ZOOM = value;
    }, (error) => {
      console.log('getMaxZoom error: ' + error);
    });
    
    CameraPreview.getZoom((value) => {
      this.currentZoom = value;
    }, (error) => {
      console.log('getZoom error: ' + error);
    });
    
    CameraPreview.getFlashMode((value) => {
      this.flashMode = value;
    }, (error) => {
      console.log('getFlashMode error: ' + error);
    });
  }

  switchCamera() {
    CameraPreview.switchCamera((value) => {
      CameraPreview.getZoom((value) => {
        console.log(value);
      }, (error) => {
        console.log('getZoom error: ' + error);
      });
    }, (error) => {
      console.log('switchCamera error: ' + error);
    });
  }

  setFlash(flashMode) {
    console.log(flashMode);
    this.flashMode = flashMode;
    CameraPreview.setFlashMode(flashMode);
  }

  async takePicture() {
    console.log('=== Camera Modal - takePicture called ===');
    const pictureOpts = {
      width: 1280,
      height: 1280,
      quality: 100
    };
    
    console.log('Picture options:', pictureOpts);
    
    CameraPreview.takePicture(pictureOpts, async (imageData) => {
      console.log('=== Camera capture success ===');
      console.log('Raw imageData:', imageData);
      
      // Stop camera immediately after capture
      CameraPreview.stopCamera();
      
      try {
        const fileUri = 'file://' + imageData;
        console.log('Starting base64 conversion...');
        
        const base64 = await this.fileUriToBase64(fileUri);
        console.log('Base64 conversion successful');
        
        this.closeModal(base64);
      } catch (error) {
        console.error('Base64 conversion failed:', error);
        this.closeModal('file://' + imageData);
      }
    }, (error) => {
      console.error('Camera takePicture error:', error);
    });
  }

  closeModal(imageData?: any) {
    console.log('=== Camera Modal - closeModal called ===');
    
    // Ensure camera is stopped
    try {
      CameraPreview.stopCamera();
    } catch (error) {
      console.log('Camera already stopped or error stopping:', error);
    }
    
    const returnData = {
      imageData: imageData,
      controlId: this.controlId
    };
    
    console.log('Dismissing modal with data');
    this.modalController.dismiss(returnData);
  }

  ionViewDidLeave() {
    try {
      CameraPreview.stopCamera();
    } catch (error) {
      console.log('Error stopping camera on leave:', error);
    }
  }

  async fileUriToBase64(fileUri: string): Promise<string> {
    try {
      const perm = await this.androidPermissions.checkPermission(
        this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE
      );
      if (!perm.hasPermission) {
        await this.androidPermissions.requestPermission(
          this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE
        );
      }
    } catch (err) {
      console.warn('Permission check/request failed:', err);
    }

    return new Promise((resolve, reject) => {
      let path = fileUri.startsWith('file://') ? fileUri.substring(7) : fileUri;
      const lastSlash = path.lastIndexOf('/');
      let dirPath = path.substring(0, lastSlash + 1);
      let fileName = path.substring(lastSlash + 1);

      // Try decoding fileName in case of URL encoding
      try {
        fileName = decodeURIComponent(fileName);
      } catch (e) {}

      // Detect and use Cordova File plugin directory constants
      let cordovaDir: string | undefined;
      if (dirPath.includes('/cache/')) {
        cordovaDir = this.file.cacheDirectory;
      } else if (dirPath.includes('/files/')) {
        cordovaDir = this.file.dataDirectory;
      } else if (dirPath.includes('/external/')) {
        cordovaDir = this.file.externalDataDirectory;
      }
      // Add more mappings as needed

      if (cordovaDir) {
        console.log('Using Cordova directory:', cordovaDir, 'fileName:', fileName);
        this.file
          .checkFile(cordovaDir, fileName)
          .then(() => this.file.readAsDataURL(cordovaDir, fileName))
          .then((base64) => resolve(base64))
          .catch((err) => {
            console.error('File check/read error (cordovaDir):', err);
            reject(err);
          });
      } else {
        // Fallback: try with parsed path (may still fail)
        console.log('Fallback to parsed dirPath:', dirPath, 'fileName:', fileName);
        this.file
          .checkFile(dirPath, fileName)
          .then(() => this.file.readAsDataURL(dirPath, fileName))
          .then((base64) => resolve(base64))
          .catch((err) => {
            console.error('File check/read error (fallback):', err);
            reject(err);
          });
      }
    });
  }
}