.camera-modal-layout {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
}

.top-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 100001;

  .close-btn {
    --background: transparent;
    --color: white;
    width: 50px;
    height: 50px;
    
    ion-icon {
      color: white;
      font-size: 28px;
    }
  }

  .camera-actions {
    display: flex;
    gap: 15px;

    ion-button {
      --background: transparent;
      --color: white;
      width: 50px;
      height: 50px;
      
      ion-icon {
        color: white;
        font-size: 24px;
      }
    }
  }
}

.camera-preview-area {
  flex: 1;
  background: black;
}

.bottom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100001;
}

.capture-button {
  --background: transparent !important;
  --border-radius: 50% !important;
  width: 90px !important;
  height: 90px !important;
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
}

.capture-circle {
  width: 80px;
  height: 80px;
  border: 6px solid white;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transition: all 0.2s ease;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.capture-button:active .capture-circle {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.7);
}