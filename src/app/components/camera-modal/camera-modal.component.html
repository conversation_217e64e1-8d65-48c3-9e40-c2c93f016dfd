<div class="camera-modal-layout">
  <!-- Top controls area -->
  <div class="top-controls">
    <ion-button fill="clear" (click)="closeModal()" class="close-btn">
      <ion-icon name="close"></ion-icon>
    </ion-button>
    
    <div class="camera-actions">
      <ion-button fill="clear" (click)="setFlash(flashMode === 'off' ? 'on' : 'off')">
        <ion-icon [name]="flashMode === 'off' ? 'flash-off' : 'flash'"></ion-icon>
      </ion-button>
      
      <ion-button fill="clear" (click)="switchCamera()">
        <ion-icon name="camera-reverse"></ion-icon>
      </ion-button>
    </div>
  </div>

  <!-- Camera preview area -->
  <div class="camera-preview-area"></div>

  <!-- Bottom capture area -->
  <div class="bottom-controls">
    <ion-button class="capture-button" fill="clear" (click)="takePicture()">
      <div class="capture-circle"></div>
    </ion-button>
  </div>
</div>