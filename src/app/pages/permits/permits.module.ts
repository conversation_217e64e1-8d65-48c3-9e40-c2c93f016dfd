import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { IonicModule } from '@ionic/angular';

import { PermitsPageRoutingModule } from './permits-routing.module';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { PermitsPage } from './permits.page';
import { AddPermitRoleComponent } from 'src/app/components/add-permit-role/add-permit-role.component';
import { CreatePermitComponent } from 'src/app/components/create-permit/create-permit.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PermitDetailsComponent } from 'src/app/components/permit-details/permit-details.component';
import { UppercaseToLowerCaseFilterPipe } from 'src/app/pipes/upper-case-to-lower-pipe';
import { StatusTextFormatFilterPipe } from 'src/app/pipes/status--text-format-pipe';
import { AddNewPermitStakeHolderComponent } from 'src/app/components/add-new-permit-stake-holder/add-new-permit-stake-holder.component';
import { TranslateModule } from '@ngx-translate/core';
import { ZXingScannerModule } from '@zxing/ngx-scanner';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { Camera } from '@awesome-cordova-plugins/camera/ngx';

import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { NgxScannerComponent } from 'src/app/components/ngx-scanner/ngx-scanner.component';
import { ImageFullScreenComponent } from 'src/app/components/image-full-screen/image-full-screen.component';
import { AnnotateImageComponent } from 'src/app/components/annotate-image/annotate-image.component';
import { PermitMapComponent } from 'src/app/components/permit-map/permit-map.component';

// import {
// 	IgxStepperModule,
// 	IgxButtonGroupModule
//  } from "igniteui-angular";
import { BarcodeScanner } from '@awesome-cordova-plugins/barcode-scanner/ngx';
import { SharedModule } from 'src/app/shared/shared.module';
import { SkillsCertificateViewComponent } from 'src/app/components/skills-certificate-view/skills-certificate-view.component';
import { EditPermitDetailsComponent } from 'src/app/components/edit-permit-details/edit-permit-details.component';
import { EditPermitAgentsComponent } from 'src/app/components/edit-permit-agents/edit-permit-agents.component';
import { EditPermitValidityComponent } from 'src/app/components/edit-permit-validity/edit-permit-validity.component';
import { AddPermitCommentsComponent } from 'src/app/components/add-permit-comments/add-permit-comments.component';
import { EditFormComponent } from 'src/app/components/edit-form/edit-form.component';
import { UserSkillsTooltipComponent } from 'src/app/components/user-skills-tooltip/user-skills-tooltip.component';
import { EditUserFacilityComponent } from 'src/app/components/edit-user-facility/edit-user-facility.component';
import { CameraModalComponent } from 'src/app/components/camera-modal/camera-modal.component';
import { OwlDateTimeModule, OwlNativeDateTimeModule } from '@danielmoncada/angular-datetime-picker';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    PermitsPageRoutingModule,
    FormsModule,
    TranslateModule,
    ZXingScannerModule,
    ReactiveFormsModule,
    SharedModule,
    OwlDateTimeModule,
    OwlNativeDateTimeModule

  //   IgxStepperModule,
	// IgxButtonGroupModule

  ],
  declarations: [
    PermitsPage,
    AddPermitRoleComponent,
    CreatePermitComponent,
    PermitDetailsComponent,
    UppercaseToLowerCaseFilterPipe,
    StatusTextFormatFilterPipe,
    AddNewPermitStakeHolderComponent,
    NgxScannerComponent,
    ImageFullScreenComponent,
    AnnotateImageComponent,
    PermitMapComponent,
    SkillsCertificateViewComponent,
    EditPermitDetailsComponent,
    EditPermitAgentsComponent,
    EditPermitValidityComponent,
    AddPermitCommentsComponent,
    EditFormComponent,
    UserSkillsTooltipComponent,
    EditUserFacilityComponent,
    CameraModalComponent
  ],
providers: [BarcodeScanner, Device, Camera, File, AndroidPermissions],
schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PermitsPageModule {}
