<!-- Loading state with shimmer effect -->
<div *ngIf="isLoading" class="shimmer-container">
  <!-- Shimmer for search card -->
  <div class="shimmer-search-card">
    <div class="shimmer-search-input"></div>
    <div class="shimmer-filters">
      <div class="shimmer-filter" *ngFor="let i of [1,2,3]"></div>
    </div>
    <div class="shimmer-button"></div>
  </div>

  <!-- Shimmer for table rows -->
  <div class="shimmer-table">
    <div class="shimmer-header"></div>
    <div class="shimmer-row" *ngFor="let i of [1,2,3,4,5]"></div>
  </div>
</div>

<!-- Search card with filters - show only when not loading AND agents exist -->
<div *ngIf="!isLoading && agentsData && agentsData.length > 0" class="search-card">
  <div class="search-row">
    <div class="search-wrapper">
      <ion-icon name="search-outline"></ion-icon>
      <input type="text" placeholder="Search agents..." [(ngModel)]="searchTerm" (input)="filterAgentsBasedOnSearch()">
      <ion-icon name="close-outline" class="clear-search" *ngIf="searchTerm" (click)="clearSearch()"></ion-icon>
    </div>

    <div class="action-buttons">
      <button class="action-btn" (click)="getAgentsServerData()">
        <ion-icon name="refresh-outline"></ion-icon>
        Refresh
      </button>
      <button class="add-btn" (click)="addUpdateModal()">
        <ion-icon name="add-outline"></ion-icon>
        Add Agent
      </button>
    </div>
  </div>
</div>

<!-- Zero data state when no agents available at all and not loading -->
<div *ngIf="!isLoading && (!agentsData || agentsData.length === 0)" class="empty-state-container">
  <div class="empty-state-content">
    <ion-icon name="business-outline" class="empty-state-icon"></ion-icon>
    <h2>No Agents Available</h2>
    <p>There are currently no agents to display.</p>
    <p>You can add a new agent or refresh to fetch agents from the server.</p>
    <div class="empty-state-actions">
      <button class="add-btn" (click)="addUpdateModal()">
        <ion-icon name="add-outline"></ion-icon>
        Add Agent
      </button>
      <button class="action-btn secondary" (click)="getAgentsServerData()">
        <ion-icon name="refresh-outline"></ion-icon>
        Refresh
      </button>
    </div>
  </div>
</div>

<!-- Regular content when agentsData has data -->
<div *ngIf="!isLoading && agentsData && agentsData.length > 0" class="agents-table-wrapper">
  <div class="agents-table">
    <div class="table-header">
      <div class="header-cell id-cell">ID</div>
      <div class="header-cell details-cell">Details</div>
      <div class="header-cell contact-cell">Contact Person</div>
      <div class="header-cell facilities-cell">Facilities</div>
      <div class="header-cell internal-cell">Is Internal</div>
      <div class="header-cell active-cell">Is Active</div>
      <div class="header-cell actions-cell">Actions</div>
    </div>

    <!-- Show filtered results when available -->
    <div class="table-row" *ngFor="let agent of filteredData || agentsData">
      <div class="table-cell id-cell" data-label="ID">{{agent.AGENT_ID}}</div>
      <div class="table-cell details-cell" data-label="Details">
        <div class="agent-name">{{agent.NAME}}</div>
        <div class="agent-address">{{agent.ADDRESS}}</div>
        <div class="agent-phone">{{agent.PHONE}}</div>
        <div class="agent-email"><a [href]="'mailto:' + agent.EMAIL" class="email-link">{{agent.EMAIL}}</a></div>
      </div>
      <div class="table-cell contact-cell" data-label="Contact Person">{{agent.CONTACT}}</div>
      <div class="table-cell facilities-cell" data-label="Facilities">
        <div class="facility-badges">
          <span class="facility-badge"
                *ngFor="let item of agent?.SelectedFacilities"
                (click)="goToFacility(item.FACILITY_ID)">
            {{item.NAME}}
          </span>
        </div>
      </div>
      <div class="table-cell internal-cell" data-label="Is Internal">
        <ion-icon *ngIf="agent.IS_INTERNAL=='true'" name="checkmark-circle" class="status-icon active"></ion-icon>
        <ion-icon *ngIf="agent.IS_INTERNAL!='true'" name="close-circle" class="status-icon inactive"></ion-icon>
      </div>
      <div class="table-cell active-cell" data-label="Is Active">
        <ion-icon *ngIf="agent.IS_ACTIVE=='true'" name="checkmark-circle" class="status-icon active"></ion-icon>
        <ion-icon *ngIf="agent.IS_ACTIVE!='true'" name="close-circle" class="status-icon inactive"></ion-icon>
      </div>
      <div class="table-cell actions-cell">
        <ion-button fill="clear" size="small" color="primary" *ngIf="isUserManagement" (click)="addAgentUsers(agent)" title="Manage Users">
          <ion-icon name="people-outline"></ion-icon>
        </ion-button>
        <ion-button fill="clear" size="small" color="primary" (click)="addUpdateModal(agent)" title="Edit Agent">
          <ion-icon name="create-outline"></ion-icon>
        </ion-button>
      </div>
    </div>

    <!-- Show empty state message when filtered list is empty but agentsData has items -->
    <div *ngIf="filteredData && filteredData.length === 0 && agentsData.length > 0" class="no-results-message">
      <div class="no-results-content">
        <ion-icon name="search-outline"></ion-icon>
        <h3 class="no-results-title">No Agents Found</h3>
        <p class="no-results-message-text">
          No agents match "<strong>{{searchTerm}}</strong>".
        </p>
        <p class="no-results-suggestion">
          Try adjusting your search terms or browse all agents below.
        </p>
        <button class="clear-search-btn" (click)="clearSearch()">
          <ion-icon name="close-circle-outline"></ion-icon>
          Clear Search
        </button>
      </div>
    </div>
  </div>
</div>
