/*

████████╗██╗  ██╗███████╗███╗   ███╗███████╗███████╗
╚══██╔══╝██║  ██║██╔════╝████╗ ████║██╔════╝██╔════╝
   ██║   ███████║█████╗  ██╔████╔██║█████╗  ███████╗
   ██║   ██╔══██║██╔══╝  ██║╚██╔╝██║██╔══╝  ╚════██║
   ██║   ██║  ██║███████╗██║ ╚═╝ ██║███████╗███████║
   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝     ╚═╝╚══════╝╚══════╝

*/

/*******************************
        Theme Selection
*******************************/

/* To override a theme for an individual element
   specify theme name below
*/
/* Global */
@site: default;
@reset: default;

/* Elements */
@button: default;
@container: default;
@divider: default;
@emoji: default;
@flag: default;
@header: default;
@icon: default;
@image: default;
@input: default;
@label: default;
@list: default;
@loader: default;
@placeholder: default;
@rail: default;
@reveal: default;
@segment: default;
@step: default;
@text: default;

/* Collections */
@breadcrumb: default;
@form: default;
@grid: default;
@menu: default;
@message: default;
@table: default;

/* Modules */
@accordion: default;
@calendar: default;
@checkbox: default;
@dimmer: default;
@dropdown: default;
@embed: default;
@flyout: default;
@modal: default;
@nag: default;
@popup: default;
@progress: default;
@slider: default;
@rating: default;
@search: default;
@shape: default;
@sidebar: default;
@sticky: default;
@tab: default;
@toast: default;
@transition: default;

/* Views */
@ad: default;
@card: default;
@comment: default;
@feed: default;
@item: default;
@statistic: default;

/*******************************
            Folders
*******************************/
@themesFolder: themes;
@siteFolder: site;

@import (multiple) "theme.less";
