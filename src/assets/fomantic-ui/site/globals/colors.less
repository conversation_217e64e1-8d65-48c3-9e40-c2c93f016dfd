/***********************************************************
  Central Color Mapping Base for all components to iterate
***********************************************************/

@colors: {
	@primary: {
		color: @primaryColor;
		light: @lightPrimaryColor;
		border: @primaryBorderColor;
		background: @primaryBackground;
		header: @primaryHeaderColor;
		boxShadow: @primaryBoxShadow;
		boxFloatShadow: @primaryBoxFloatingShadow;
		text: @primaryTextColor;
		lightText: @lightPrimaryTextColor;
		hoverText: @primaryHoverTextColor;
		focus: @primaryColorFocus;
		lightFocus: @lightPrimaryColorFocus;
		down: @primaryColorDown;
		lightDown: @lightPrimaryColorDown;
		active: @primaryColorActive;
		lightActive: @lightPrimaryColorActive;
		shadow: @primaryTextShadow;
		lightShadow: @lightPrimaryTextShadow;
		hover: @primaryColorHover;
		lightHover: @lightPrimaryColorHover;
		ribbon: @primaryRibbonShadow;
		invertedRibbon: @primaryInvertedRibbonShadow;
		tertiary: @primaryTertiaryColor;
		tertiaryHover: @primaryTertiaryColorHover;
		tertiaryFocus: @primaryTertiaryColorFocus;
		tertiaryActive: @primaryTertiaryColorActive;
		bright: @primaryBright;
		brightHover: @primaryBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@secondary: {
		color: @secondaryColor;
		light: @lightSecondaryColor;
		border: @secondaryBorderColor;
		background: @secondaryBackground;
		header: @secondaryHeaderColor;
		boxShadow: @secondaryBoxShadow;
		boxFloatShadow: @secondaryBoxFloatingShadow;
		text: @secondaryTextColor;
		lightText: @lightSecondaryTextColor;
		hoverText: @secondaryHoverTextColor;
		focus: @secondaryColorFocus;
		lightFocus: @lightSecondaryColorFocus;
		down: @secondaryColorDown;
		lightDown: @lightSecondaryColorDown;
		active: @secondaryColorActive;
		lightActive: @lightSecondaryColorActive;
		shadow: @secondaryTextShadow;
		lightShadow: @lightSecondaryTextShadow;
		hover: @secondaryColorHover;
		lightHover: @lightSecondaryColorHover;
		ribbon: @secondaryRibbonShadow;
		invertedRibbon: @secondaryInvertedRibbonShadow;
		tertiary: @secondaryTertiaryColor;
		tertiaryHover: @secondaryTertiaryColorHover;
		tertiaryFocus: @secondaryTertiaryColorFocus;
		tertiaryActive: @secondaryTertiaryColorActive;
		bright: @secondaryBright;
		brightHover: @secondaryBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@red: {
		color: @red;
		light: @lightRed;
		border: @redBorderColor;
		background: @redBackground;
		header: @redHeaderColor;
		boxShadow: @redBoxShadow;
		boxFloatShadow: @redBoxFloatingShadow;
		text: @redTextColor;
		lightText: @lightRedTextColor;
		hoverText: @redHoverTextColor;
		focus: @redFocus;
		lightFocus: @lightRedFocus;
		down: @redDown;
		lightDown: @lightRedDown;
		active: @redActive;
		lightActive: @lightRedActive;
		shadow: @redTextShadow;
		lightShadow: @lightRedTextShadow;
		hover: @redHover;
		lightHover: @lightRedHover;
		ribbon: @redRibbonShadow;
		invertedRibbon: @redInvertedRibbonShadow;
		tertiary: @redTertiaryColor;
		tertiaryHover: @redTertiaryColorHover;
		tertiaryFocus: @redTertiaryColorFocus;
		tertiaryActive: @redTertiaryColorActive;
		bright: @redBright;
		brightHover: @redBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@orange: {
		color: @orange;
		light: @lightOrange;
		border: @orangeBorderColor;
		background: @orangeBackground;
		header: @orangeHeaderColor;
		boxShadow: @orangeBoxShadow;
		boxFloatShadow: @orangeBoxFloatingShadow;
		text: @orangeTextColor;
		lightText: @lightOrangeTextColor;
		hoverText: @orangeHoverTextColor;
		focus: @orangeFocus;
		lightFocus: @lightOrangeFocus;
		down: @orangeDown;
		lightDown: @lightOrangeDown;
		active: @orangeActive;
		lightActive: @lightOrangeActive;
		shadow: @orangeTextShadow;
		lightShadow: @lightOrangeTextShadow;
		hover: @orangeHover;
		lightHover: @lightOrangeHover;
		ribbon: @orangeRibbonShadow;
		invertedRibbon: @orangeInvertedRibbonShadow;
		tertiary: @orangeTertiaryColor;
		tertiaryHover: @orangeTertiaryColorHover;
		tertiaryFocus: @orangeTertiaryColorFocus;
		tertiaryActive: @orangeTertiaryColorActive;
		bright: @orangeBright;
		brightHover: @orangeBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@yellow: {
		color: @yellow;
		light: @lightYellow;
		border: @yellowBorderColor;
		background: @yellowBackground;
		header: @yellowHeaderColor;
		boxShadow: @yellowBoxShadow;
		boxFloatShadow: @yellowBoxFloatingShadow;
		text: @yellowTextColor;
		lightText: @lightYellowTextColor;
		hoverText: @yellowHoverTextColor;
		focus: @yellowFocus;
		lightFocus: @lightYellowFocus;
		down: @yellowDown;
		lightDown: @lightYellowDown;
		active: @yellowActive;
		lightActive: @lightYellowActive;
		shadow: @yellowTextShadow;
		lightShadow: @lightYellowTextShadow;
		hover: @yellowHover;
		lightHover: @lightYellowHover;
		ribbon: @yellowRibbonShadow;
		invertedRibbon: @yellowInvertedRibbonShadow;
		tertiary: @yellowTertiaryColor;
		tertiaryHover: @yellowTertiaryColorHover;
		tertiaryFocus: @yellowTertiaryColorFocus;
		tertiaryActive: @yellowTertiaryColorActive;
		bright: @yellowBright;
		brightHover: @yellowBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@olive: {
		color: @olive;
		light: @lightOlive;
		border: @oliveBorderColor;
		background: @oliveBackground;
		header: @oliveHeaderColor;
		boxShadow: @oliveBoxShadow;
		boxFloatShadow: @oliveBoxFloatingShadow;
		text: @oliveTextColor;
		lightText: @lightOliveTextColor;
		hoverText: @oliveHoverTextColor;
		focus: @oliveFocus;
		lightFocus: @lightOliveFocus;
		down: @oliveDown;
		lightDown: @lightOliveDown;
		active: @oliveActive;
		lightActive: @lightOliveActive;
		shadow: @oliveTextShadow;
		lightShadow: @lightOliveTextShadow;
		hover: @oliveHover;
		lightHover: @lightOliveHover;
		ribbon: @oliveRibbonShadow;
		invertedRibbon: @oliveInvertedRibbonShadow;
		tertiary: @oliveTertiaryColor;
		tertiaryHover: @oliveTertiaryColorHover;
		tertiaryFocus: @oliveTertiaryColorFocus;
		tertiaryActive: @oliveTertiaryColorActive;
		bright: @oliveBright;
		brightHover: @oliveBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@green: {
		color: @green;
		light: @lightGreen;
		border: @greenBorderColor;
		background: @greenBackground;
		header: @greenHeaderColor;
		boxShadow: @greenBoxShadow;
		boxFloatShadow: @greenBoxFloatingShadow;
		text: @greenTextColor;
		lightText: @lightGreenTextColor;
		hoverText: @greenHoverTextColor;
		focus: @greenFocus;
		lightFocus: @lightGreenFocus;
		down: @greenDown;
		lightDown: @lightGreenDown;
		active: @greenActive;
		lightActive: @lightGreenActive;
		shadow: @greenTextShadow;
		lightShadow: @lightGreenTextShadow;
		hover: @greenHover;
		lightHover: @lightGreenHover;
		ribbon: @greenRibbonShadow;
		invertedRibbon: @greenInvertedRibbonShadow;
		tertiary: @greenTertiaryColor;
		tertiaryHover: @greenTertiaryColorHover;
		tertiaryFocus: @greenTertiaryColorFocus;
		tertiaryActive: @greenTertiaryColorActive;
		bright: @greenBright;
		brightHover: @greenBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@teal: {
		color: @teal;
		light: @lightTeal;
		border: @tealBorderColor;
		background: @tealBackground;
		header: @tealHeaderColor;
		boxShadow: @tealBoxShadow;
		boxFloatShadow: @tealBoxFloatingShadow;
		text: @tealTextColor;
		lightText: @lightTealTextColor;
		hoverText: @tealHoverTextColor;
		focus: @tealFocus;
		lightFocus: @lightTealFocus;
		down: @tealDown;
		lightDown: @lightTealDown;
		active: @tealActive;
		lightActive: @lightTealActive;
		shadow: @tealTextShadow;
		lightShadow: @lightTealTextShadow;
		hover: @tealHover;
		lightHover: @lightTealHover;
		ribbon: @tealRibbonShadow;
		invertedRibbon: @tealInvertedRibbonShadow;
		tertiary: @tealTertiaryColor;
		tertiaryHover: @tealTertiaryColorHover;
		tertiaryFocus: @tealTertiaryColorFocus;
		tertiaryActive: @tealTertiaryColorActive;
		bright: @tealBright;
		brightHover: @tealBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@blue: {
		color: @blue;
		light: @lightBlue;
		border: @blueBorderColor;
		background: @blueBackground;
		header: @blueHeaderColor;
		boxShadow: @blueBoxShadow;
		boxFloatShadow: @blueBoxFloatingShadow;
		text: @blueTextColor;
		lightText: @lightBlueTextColor;
		hoverText: @blueHoverTextColor;
		focus: @blueFocus;
		lightFocus: @lightBlueFocus;
		down: @blueDown;
		lightDown: @lightBlueDown;
		active: @blueActive;
		lightActive: @lightBlueActive;
		shadow: @blueTextShadow;
		lightShadow: @lightBlueTextShadow;
		hover: @blueHover;
		lightHover: @lightBlueHover;
		ribbon: @blueRibbonShadow;
		invertedRibbon: @blueInvertedRibbonShadow;
		tertiary: @blueTertiaryColor;
		tertiaryHover: @blueTertiaryColorHover;
		tertiaryFocus: @blueTertiaryColorFocus;
		tertiaryActive: @blueTertiaryColorActive;
		bright: @blueBright;
		brightHover: @blueBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@violet: {
		color: @violet;
		light: @lightViolet;
		border: @violetBorderColor;
		background: @violetBackground;
		header: @violetHeaderColor;
		boxShadow: @violetBoxShadow;
		boxFloatShadow: @violetBoxFloatingShadow;
		text: @violetTextColor;
		lightText: @lightVioletTextColor;
		hoverText: @violetHoverTextColor;
		focus: @violetFocus;
		lightFocus: @lightVioletFocus;
		down: @violetDown;
		lightDown: @lightVioletDown;
		active: @violetActive;
		lightActive: @lightVioletActive;
		shadow: @violetTextShadow;
		lightShadow: @lightVioletTextShadow;
		hover: @violetHover;
		lightHover: @lightVioletHover;
		ribbon: @violetRibbonShadow;
		invertedRibbon: @violetInvertedRibbonShadow;
		tertiary: @violetTertiaryColor;
		tertiaryHover: @violetTertiaryColorHover;
		tertiaryFocus: @violetTertiaryColorFocus;
		tertiaryActive: @violetTertiaryColorActive;
		bright: @violetBright;
		brightHover: @violetBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@purple: {
		color: @purple;
		light: @lightPurple;
		border: @purpleBorderColor;
		background: @purpleBackground;
		header: @purpleHeaderColor;
		boxShadow: @purpleBoxShadow;
		boxFloatShadow: @purpleBoxFloatingShadow;
		text: @purpleTextColor;
		lightText: @lightPurpleTextColor;
		hoverText: @purpleHoverTextColor;
		focus: @purpleFocus;
		lightFocus: @lightPurpleFocus;
		down: @purpleDown;
		lightDown: @lightPurpleDown;
		active: @purpleActive;
		lightActive: @lightPurpleActive;
		shadow: @purpleTextShadow;
		lightShadow: @lightPurpleTextShadow;
		hover: @purpleHover;
		lightHover: @lightPurpleHover;
		ribbon: @purpleRibbonShadow;
		invertedRibbon: @purpleInvertedRibbonShadow;
		tertiary: @purpleTertiaryColor;
		tertiaryHover: @purpleTertiaryColorHover;
		tertiaryFocus: @purpleTertiaryColorFocus;
		tertiaryActive: @purpleTertiaryColorActive;
		bright: @purpleBright;
		brightHover: @purpleBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@pink: {
		color: @pink;
		light: @lightPink;
		border: @pinkBorderColor;
		background: @pinkBackground;
		header: @pinkHeaderColor;
		boxShadow: @pinkBoxShadow;
		boxFloatShadow: @pinkBoxFloatingShadow;
		text: @pinkTextColor;
		lightText: @lightPinkTextColor;
		hoverText: @pinkHoverTextColor;
		focus: @pinkFocus;
		lightFocus: @lightPinkFocus;
		down: @pinkDown;
		lightDown: @lightPinkDown;
		active: @pinkActive;
		lightActive: @lightPinkActive;
		shadow: @pinkTextShadow;
		lightShadow: @lightPinkTextShadow;
		hover: @pinkHover;
		lightHover: @lightPinkHover;
		ribbon: @pinkRibbonShadow;
		invertedRibbon: @pinkInvertedRibbonShadow;
		tertiary: @pinkTertiaryColor;
		tertiaryHover: @pinkTertiaryColorHover;
		tertiaryFocus: @pinkTertiaryColorFocus;
		tertiaryActive: @pinkTertiaryColorActive;
		bright: @pinkBright;
		brightHover: @pinkBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@brown: {
		color: @brown;
		light: @lightBrown;
		border: @brownBorderColor;
		background: @brownBackground;
		header: @brownHeaderColor;
		boxShadow: @brownBoxShadow;
		boxFloatShadow: @brownBoxFloatingShadow;
		text: @brownTextColor;
		lightText: @lightBrownTextColor;
		hoverText: @brownHoverTextColor;
		focus: @brownFocus;
		lightFocus: @lightBrownFocus;
		down: @brownDown;
		lightDown: @lightBrownDown;
		active: @brownActive;
		lightActive: @lightBrownActive;
		shadow: @brownTextShadow;
		lightShadow: @lightBrownTextShadow;
		hover: @brownHover;
		lightHover: @lightBrownHover;
		ribbon: @brownRibbonShadow;
		invertedRibbon: @brownInvertedRibbonShadow;
		tertiary: @brownTertiaryColor;
		tertiaryHover: @brownTertiaryColorHover;
		tertiaryFocus: @brownTertiaryColorFocus;
		tertiaryActive: @brownTertiaryColorActive;
		bright: @brownBright;
		brightHover: @brownBrightHover;
		isDark: false;
		isVeryDark: false;
	};
	@grey: {
		color: @grey;
		light: @lightGrey;
		border: @greyBorderColor;
		background: @greyBackground;
		header: @greyHeaderColor;
		boxShadow: @greyBoxShadow;
		boxFloatShadow: @greyBoxFloatingShadow;
		text: @greyTextColor;
		lightText: @lightGreyTextColor;
		hoverText: @greyHoverTextColor;
		focus: @greyFocus;
		lightFocus: @lightGreyFocus;
		down: @greyDown;
		lightDown: @lightGreyDown;
		active: @greyActive;
		lightActive: @lightGreyActive;
		shadow: @greyTextShadow;
		lightShadow: @lightGreyTextShadow;
		hover: @greyHover;
		lightHover: @lightGreyHover;
		ribbon: @greyRibbonShadow;
		invertedRibbon: @greyInvertedRibbonShadow;
		tertiary: @greyTertiaryColor;
		tertiaryHover: @greyTertiaryColorHover;
		tertiaryFocus: @greyTertiaryColorFocus;
		tertiaryActive: @greyTertiaryColorActive;
		bright: @greyBright;
		brightHover: @greyBrightHover;
		isDark: true;
		isVeryDark: false;
	};
	@black: {
		color: @black;
		light: @lightBlack;
		border: @blackBorderColor;
		background: @blackBackground;
		header: @blackHeaderColor;
		boxShadow: @blackBoxShadow;
		boxFloatShadow: @blackBoxFloatingShadow;
		text: @blackTextColor;
		lightText: @lightBlackTextColor;
		hoverText: @blackHoverTextColor;
		focus: @blackFocus;
		lightFocus: @lightBlackFocus;
		down: @blackDown;
		lightDown: @lightBlackDown;
		active: @blackActive;
		lightActive: @lightBlackActive;
		shadow: @blackTextShadow;
		lightShadow: @lightBlackTextShadow;
		hover: @blackHover;
		lightHover: @lightBlackHover;
		ribbon: @blackRibbonShadow;
		invertedRibbon: @blackInvertedRibbonShadow;
		tertiary: @blackTertiaryColor;
		tertiaryHover: @blackTertiaryColorHover;
		tertiaryFocus: @blackTertiaryColorFocus;
		tertiaryActive: @blackTertiaryColorActive;
		bright: @blackBright;
		brightHover: @blackBrightHover;
		isDark: true;
		isVeryDark: true;
	};
};

/***********************************************************
  Color Mapping Base for form components to iterate
***********************************************************/

@formStates: {
	@error: {
		color: @formErrorColor;
		background: @formErrorBackground;
		borderColor: @formErrorBorder;
		borderRadius: @inputErrorBorderRadius;
		boxShadow: @inputErrorBoxShadow;
		cornerLabelColor: @white;
		labelBackground: @formErrorLabelBackground;

		dropdownLabelColor: @dropdownErrorLabelColor;
		dropdownLabelBackground: @dropdownErrorLabelBackground;
		dropdownHoverBackground: @dropdownErrorHoverBackground;
		dropdownSelectedBackground: @dropdownErrorSelectedBackground;
		dropdownActiveBackground: @dropdownErrorActiveBackground;

		inputAutoFillBackground: @inputAutoFillErrorBackground;
		inputAutoFillBorderColor: @inputAutoFillErrorBorder;
		inputFocusBackground: @inputErrorFocusBackground;
		inputFocusColor: @inputErrorFocusColor;
		inputFocusBorderColor: @inputErrorFocusBorder;
		inputFocusBoxShadow: @inputErrorFocusBoxShadow;
		inputPlaceholderColor: @inputErrorPlaceholderColor;
		inputPlaceholderFocusColor: @inputErrorPlaceholderFocusColor;

		transparentBackground: @transparentFormErrorBackground;
		transparentColor: @transparentFormErrorColor;
	};

	@info: {
		color: @formInfoColor;
		background: @formInfoBackground;
		borderColor: @formInfoBorder;
		borderRadius: @inputInfoBorderRadius;
		boxShadow: @inputInfoBoxShadow;
		cornerLabelColor: @white;
		labelBackground: @formInfoLabelBackground;

		dropdownLabelColor: @dropdownInfoLabelColor;
		dropdownLabelBackground: @dropdownInfoLabelBackground;
		dropdownHoverBackground: @dropdownInfoHoverBackground;
		dropdownSelectedBackground: @dropdownInfoSelectedBackground;
		dropdownActiveBackground: @dropdownInfoActiveBackground;

		inputAutoFillBackground: @inputAutoFillInfoBackground;
		inputAutoFillBorderColor: @inputAutoFillInfoBorder;
		inputFocusBackground: @inputInfoFocusBackground;
		inputFocusColor: @inputInfoFocusColor;
		inputFocusBorderColor: @inputInfoFocusBorder;
		inputFocusBoxShadow: @inputInfoFocusBoxShadow;
		inputPlaceholderColor: @inputInfoPlaceholderColor;
		inputPlaceholderFocusColor: @inputInfoPlaceholderFocusColor;

		transparentBackground: @transparentFormInfoBackground;
		transparentColor: @transparentFormInfoColor;
	};

	@success: {
		color: @formSuccessColor;
		background: @formSuccessBackground;
		borderColor: @formSuccessBorder;
		borderRadius: @inputSuccessBorderRadius;
		boxShadow: @inputSuccessBoxShadow;
		cornerLabelColor: @white;
		labelBackground: @formSuccessLabelBackground;

		dropdownLabelColor: @dropdownSuccessLabelColor;
		dropdownLabelBackground: @dropdownSuccessLabelBackground;
		dropdownHoverBackground: @dropdownSuccessHoverBackground;
		dropdownSelectedBackground: @dropdownSuccessSelectedBackground;
		dropdownActiveBackground: @dropdownSuccessActiveBackground;

		inputAutoFillBackground: @inputAutoFillSuccessBackground;
		inputAutoFillBorderColor: @inputAutoFillSuccessBorder;
		inputFocusBackground: @inputSuccessFocusBackground;
		inputFocusColor: @inputSuccessFocusColor;
		inputFocusBorderColor: @inputSuccessFocusBorder;
		inputFocusBoxShadow: @inputSuccessFocusBoxShadow;
		inputPlaceholderColor: @inputSuccessPlaceholderColor;
		inputPlaceholderFocusColor: @inputSuccessPlaceholderFocusColor;

		transparentBackground: @transparentFormSuccessBackground;
		transparentColor: @transparentFormSuccessColor;
	};

	@warning: {
		color: @formWarningColor;
		background: @formWarningBackground;
		borderColor: @formWarningBorder;
		borderRadius: @inputWarningBorderRadius;
		boxShadow: @inputWarningBoxShadow;
		cornerLabelColor: @white;
		labelBackground: @formWarningLabelBackground;

		dropdownLabelColor: @dropdownWarningLabelColor;
		dropdownLabelBackground: @dropdownWarningLabelBackground;
		dropdownHoverBackground: @dropdownWarningHoverBackground;
		dropdownSelectedBackground: @dropdownWarningSelectedBackground;
		dropdownActiveBackground: @dropdownWarningActiveBackground;

		inputAutoFillBackground: @inputAutoFillWarningBackground;
		inputAutoFillBorderColor: @inputAutoFillWarningBorder;
		inputFocusBackground: @inputWarningFocusBackground;
		inputFocusColor: @inputWarningFocusColor;
		inputFocusBorderColor: @inputWarningFocusBorder;
		inputFocusBoxShadow: @inputWarningFocusBoxShadow;
		inputPlaceholderColor: @inputWarningPlaceholderColor;
		inputPlaceholderFocusColor: @inputWarningPlaceholderFocusColor;

		transparentBackground: @transparentFormWarningBackground;
		transparentColor: @transparentFormWarningColor;
	};
};

@textStates: {
	@error: {
		color: @negativeColor;
	};
	@info: {
		color: @infoColor;
	};
	@success: {
		color: @positiveColor;
	};
	@warning: {
		color: @warningColor;
	};
};
